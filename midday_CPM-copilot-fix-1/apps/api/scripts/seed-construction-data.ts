import { connectDb } from "../src/db";
import { constructionProjects, siteMeasurements, teams, users } from "../src/db/schema";
import { eq } from "drizzle-orm";

async function seedConstructionData() {
  console.log("🌱 Seeding construction data...");

  try {
    // Connect to database
    const db = await connectDb();

    // Get the first team and user for testing
    const [team] = await db.select().from(teams).limit(1);
    const [user] = await db.select().from(users).limit(1);

    if (!team || !user) {
      console.log("❌ No team or user found. Please create a team and user first.");
      return;
    }

    console.log(`Using team: ${team.name} (${team.id})`);
    console.log(`Using user: ${user.fullName} (${user.id})`);

    // Create sample construction project
    const [project] = await db
      .insert(constructionProjects)
      .values({
        name: "Smart Construction - Kometsu South Harbor",
        description: "Modern construction project with advanced 3D visualization and real-time collaboration features",
        location: "Kometsu - South Harbor",
        address: "123 Harbor Drive, Kometsu City, KC 12345",
        status: "in_progress",
        currentPhase: "foundation",
        startDate: "2024-01-15",
        endDate: "2024-12-15",
        estimatedCost: 2500000,
        actualCost: 1200000,
        currency: "USD",
        completionPercentage: 66,
        siteArea: 50000,
        buildingArea: 25000,
        teamId: team.id,
        createdBy: user.id,
        contractorInfo: {
          name: "Harbor Construction LLC",
          contact: "John Smith",
          phone: "+****************",
          email: "<EMAIL>"
        },
        permitInfo: {
          buildingPermit: "BP-2024-001",
          issuedDate: "2024-01-10",
          expiryDate: "2025-01-10"
        },
        siteCoordinates: {
          lat: 40.7128,
          lng: -74.0060,
          elevation: 15.5
        }
      })
      .returning();

    console.log(`✅ Created project: ${project.name} (${project.id})`);

    // Create sample site measurements
    const measurements = [
      {
        projectId: project.id,
        teamId: team.id,
        userId: user.id,
        measurementType: "cut_fill_analysis",
        measuredBy: user.id,
        cutVolume: "250395",
        fillVolume: "23995",
        totalArea: "45000",
        perimeter: "850",
        accuracy: "0.1",
        equipment: "GPS Survey Equipment",
        notes: "Initial site survey with cut/fill calculations",
        measurementDate: "2024-01-20T10:00:00Z",
        areaGeometry: {
          type: "Polygon",
          coordinates: [[
            [-74.0065, 40.7135],
            [-74.0055, 40.7135],
            [-74.0055, 40.7120],
            [-74.0065, 40.7120],
            [-74.0065, 40.7135]
          ]]
        },
        elevationData: {
          points: [
            { x: 0, y: 0, elevation: -20 },
            { x: 14, y: 0, elevation: -15 },
            { x: 28, y: 0, elevation: -9.77 },
            { x: 41, y: 0, elevation: -6.68 },
            { x: 55, y: 0, elevation: 0 }
          ],
          crossSection: "A-A",
          datum: "NAVD88"
        }
      },
      {
        projectId: project.id,
        teamId: team.id,
        userId: user.id,
        measurementType: "progress_survey",
        measuredBy: user.id,
        cutVolume: "180000",
        fillVolume: "18000",
        totalArea: "45000",
        perimeter: "850",
        accuracy: "0.1",
        equipment: "Drone Survey",
        notes: "Monthly progress survey - foundation phase 66% complete",
        measurementDate: "2024-06-15T14:30:00Z",
        areaGeometry: {
          type: "Polygon",
          coordinates: [[
            [-74.0065, 40.7135],
            [-74.0055, 40.7135],
            [-74.0055, 40.7120],
            [-74.0065, 40.7120],
            [-74.0065, 40.7135]
          ]]
        },
        elevationData: {
          points: [
            { x: 0, y: 0, elevation: -15 },
            { x: 14, y: 0, elevation: -10 },
            { x: 28, y: 0, elevation: -5.5 },
            { x: 41, y: 0, elevation: -3.2 },
            { x: 55, y: 0, elevation: 2.1 }
          ],
          crossSection: "A-A",
          datum: "NAVD88"
        }
      }
    ];

    for (const measurement of measurements) {
      const [created] = await db
        .insert(siteMeasurements)
        .values(measurement)
        .returning();
      
      console.log(`✅ Created measurement: ${created.measurementType} (${created.id})`);
    }

    console.log("🎉 Construction data seeded successfully!");
    console.log(`\n📍 Test the project at: http://localhost:3001/construction/${project.id}`);

  } catch (error) {
    console.error("❌ Error seeding construction data:", error);
  }
}

// Run the seed function
seedConstructionData().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error("❌ Seed script failed:", error);
  process.exit(1);
});
