# Database Seeding Instructions

## Overview
The application requires certain tables to have data for the queries to work properly. The following tables need to be populated:

1. **invoice_templates** - Invoice configuration for teams
2. **bank_accounts** - Bank account information
3. **transactions** - Financial transactions
4. **transaction_categories** - Transaction categorization

## Prerequisites
1. Ensure you have the database running and accessible
2. The `.env.local` file should have the `DATABASE_SESSION_POOLER` URL configured
3. Install dependencies: `bun install` or `npm install`

## Running the Seed Process

### Step 1: Apply Database Migrations
First, ensure all migrations are applied, including the new function migrations:

```bash
cd apps/api

# Apply the base schema
bun run db:push

# Or if using drizzle-kit directly
bunx drizzle-kit push
```

### Step 2: Apply Function Migrations
The seed data requires certain database functions to exist. Apply them manually:

```bash
# Connect to your database and run the SQL files:
# 1. apps/api/migrations/0001_add_missing_functions.sql
# 2. apps/api/migrations/0002_add_private_schema.sql
```

You can apply these using your database client or command line:

```bash
# Example using psql
psql $DATABASE_SESSION_POOLER < migrations/0001_add_missing_functions.sql
psql $DATABASE_SESSION_POOLER < migrations/0002_add_private_schema.sql
```

### Step 3: Run the Seed Script
After the migrations are applied, run the seed script:

```bash
cd apps/api
bun run db:seed
```

## What the Seed Script Does

The seed script will:

1. **Create a test user** (if not exists) with ID `test-user-001`
2. **Create an invoice template** with default settings for the team
3. **Create two bank accounts**:
   - Main Business Account (checking) with $50,000 balance
   - Company Credit Card with -$2,500 balance
4. **Create transaction categories**: Office Supplies, Software, Travel, Meals, Income, Other
5. **Create sample transactions**: 5 sample transactions showing various types of business activities

## Verifying the Seed

After running the seed, you can verify the data was created:

1. Check the console output for success messages
2. The application should load without "No data available" errors
3. Bank accounts should appear in the UI
4. Sample transactions should be visible

## Troubleshooting

If you encounter errors:

1. **"relation does not exist"**: Ensure all migrations are applied first
2. **"function does not exist"**: Apply the function migration files
3. **Connection errors**: Verify your DATABASE_SESSION_POOLER URL is correct
4. **Permission errors**: Ensure your database user has proper permissions

## Re-running the Seed

The seed script is idempotent - it checks for existing data before creating new records. You can safely run it multiple times without duplicating data.