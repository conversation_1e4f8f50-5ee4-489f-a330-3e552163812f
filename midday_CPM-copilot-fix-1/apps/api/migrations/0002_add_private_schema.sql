-- Create private schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS private;

-- Function to get teams for authenticated user
CREATE OR REPLACE FUNCTION private.get_teams_for_authenticated_user()
RETURNS SETOF UUID AS $$
DECLARE
  user_id UUID;
BEGIN
  -- Get the authenticated user ID
  user_id := auth.uid();
  
  IF user_id IS NULL THEN
    RETURN;
  END IF;
  
  -- Return all teams the user is a member of
  RETURN QUERY
  SELECT DISTINCT uot.team_id
  FROM users_on_team uot
  WHERE uot.user_id = user_id;
  
  -- Also include the team from the users table if set
  RETURN QUERY
  SELECT u.team_id
  FROM users u
  WHERE u.id = user_id
    AND u.team_id IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create auth schema functions if they don't exist
CREATE SCHEMA IF NOT EXISTS auth;

-- Mock auth.uid() function for development
CREATE OR REPLACE FUNCTION auth.uid()
R<PERSON>URNS UUID AS $$
BEGIN
  -- In production, this would return the actual authenticated user ID
  -- For development/testing, return a test user ID
  RETURN '00000000-0000-0000-0000-000000000001'::UUID;
END;
$$ LANGUAGE plpgsql;

-- Mock auth.jwt() function for development
CREATE OR REPLACE FUNCTION auth.jwt()
RETURNS JSON AS $$
BEGIN
  -- In production, this would return the actual JWT
  -- For development/testing, return a mock JWT
  RETURN '{"email": "<EMAIL>"}'::JSON;
END;
$$ LANGUAGE plpgsql;