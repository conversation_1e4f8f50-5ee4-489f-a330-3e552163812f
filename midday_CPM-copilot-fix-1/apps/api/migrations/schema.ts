import { pgTable, pgPolicy, uuid, timestamp, text, boolean, index, foreignKey, unique, jsonb, bigint, numeric, date, varchar, smallint, integer, vector, point, json, primaryKey, pgView, pgMaterializedView, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const accountType = pgEnum("account_type", ['depository', 'credit', 'other_asset', 'loan', 'other_liability'])
export const bankProviders = pgEnum("bank_providers", ['gocardless', 'plaid', 'teller', 'enablebanking', 'pluggy'])
export const connectionStatus = pgEnum("connection_status", ['disconnected', 'connected', 'unknown'])
export const constructionPhase = pgEnum("construction_phase", ['planning', 'permits', 'site_prep', 'foundation', 'framing', 'roofing', 'rough_ins', 'insulation', 'drywall', 'flooring', 'cabinets', 'final_ins', 'exterior', 'final_inspection', 'completed'])
export const constructionProjectStatus = pgEnum("construction_project_status", ['planning', 'in_progress', 'on_hold', 'completed', 'cancelled'])
export const constructionRole = pgEnum("construction_role", ['admin', 'management', 'foreman', 'worker', 'subcontractor', 'client'])
export const documentProcessingStatus = pgEnum("document_processing_status", ['pending', 'processing', 'completed', 'failed'])
export const inboxAccountProviders = pgEnum("inbox_account_providers", ['gmail', 'outlook'])
export const inboxStatus = pgEnum("inbox_status", ['processing', 'pending', 'archived', 'new', 'deleted', 'done'])
export const inboxType = pgEnum("inbox_type", ['invoice', 'expense'])
export const inspectionResult = pgEnum("inspection_result", ['passed', 'failed', 'conditional', 'pending'])
export const invoiceDeliveryType = pgEnum("invoice_delivery_type", ['create', 'create_and_send', 'scheduled'])
export const invoiceSize = pgEnum("invoice_size", ['a4', 'letter'])
export const invoiceStatus = pgEnum("invoice_status", ['draft', 'overdue', 'paid', 'unpaid', 'canceled'])
export const plans = pgEnum("plans", ['trial', 'starter', 'pro'])
export const projectPriority = pgEnum("project_priority", ['low', 'medium', 'high', 'critical'])
export const projectStatus = pgEnum("project_status", ['Planning', 'Pre-construction', 'In Progress', 'On Hold', 'Completed', 'Warranty'])
export const projectType = pgEnum("project_type", ['residential_construction', 'commercial_construction', 'renovation', 'infrastructure', 'maintenance', 'custom'])
export const reportTypes = pgEnum("reportTypes", ['profit', 'revenue', 'burn_rate', 'expense'])
export const resourceStatus = pgEnum("resource_status", ['available', 'in_use', 'maintenance', 'retired'])
export const resourceType = pgEnum("resource_type", ['labor', 'equipment', 'material', 'subcontractor', 'permit'])
export const safetySeverity = pgEnum("safety_severity", ['minor', 'moderate', 'severe', 'critical'])
export const taskStatus = pgEnum("task_status", ['not_started', 'in_progress', 'on_hold', 'completed', 'cancelled', 'blocked'])
export const teamRoles = pgEnum("teamRoles", ['owner', 'member', 'admin', 'management', 'foreman'])
export const trackerStatus = pgEnum("trackerStatus", ['in_progress', 'completed'])
export const transactionCategories = pgEnum("transactionCategories", ['travel', 'office_supplies', 'meals', 'software', 'rent', 'income', 'equipment', 'transfer', 'internet_and_telephone', 'facilities_expenses', 'activity', 'uncategorized', 'taxes', 'other', 'salary', 'fees'])
export const transactionMethods = pgEnum("transactionMethods", ['payment', 'card_purchase', 'card_atm', 'transfer', 'other', 'unknown', 'ach', 'interest', 'deposit', 'wire', 'fee'])
export const transactionStatus = pgEnum("transactionStatus", ['posted', 'pending', 'excluded', 'completed', 'archived'])
export const transactionFrequency = pgEnum("transaction_frequency", ['weekly', 'biweekly', 'monthly', 'semi_monthly', 'annually', 'irregular', 'unknown'])


export const teams = pgTable("teams", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	name: text(),
	logoUrl: text("logo_url"),
	inboxId: text("inbox_id").default(generate_inbox(10)),
	email: text(),
	inboxEmail: text("inbox_email"),
	inboxForwarding: boolean("inbox_forwarding").default(true),
	baseCurrency: text("base_currency"),
	documentClassification: boolean("document_classification").default(false),
	flags: text().array(),
	canceledAt: timestamp("canceled_at", { withTimezone: true, mode: 'string' }),
	plan: text().default('trial'),
	countryCode: text("country_code"),
}, (table) => [
	pgPolicy("Teams can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"], using: sql`true` }),
	pgPolicy("Enable insert for authenticated users only", { as: "permissive", for: "insert", to: ["authenticated"] }),
	pgPolicy("Invited users can select team if they are invited.", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Teams can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Teams can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
]);

export const projectPermissions = pgTable("project_permissions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	userId: uuid("user_id").notNull(),
	role: constructionRole().notNull(),
	permissions: jsonb().default({}),
	grantedBy: uuid("granted_by"),
	grantedAt: timestamp("granted_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_project_permissions_project").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_permissions_role").using("btree", table.role.asc().nullsLast().op("enum_ops")),
	index("idx_project_permissions_user").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.grantedBy],
			foreignColumns: [users.id],
			name: "project_permissions_granted_by_fkey"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_permissions_project_id_fkey"
		}),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_permissions_user_id_fkey"
		}),
	unique("project_permissions_project_id_user_id_key").on(table.projectId, table.userId),
]);

export const trackerEntries = pgTable("tracker_entries", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	duration: bigint({ mode: "number" }),
	projectId: uuid("project_id"),
	start: timestamp({ withTimezone: true, mode: 'string' }),
	stop: timestamp({ withTimezone: true, mode: 'string' }),
	assignedId: uuid("assigned_id"),
	teamId: uuid("team_id"),
	description: text(),
	rate: numeric(),
	currency: text(),
	billed: boolean().default(false),
	date: date().defaultNow(),
}, (table) => [
	index("idx_tracker_entries_project_assigned").using("btree", table.projectId.asc().nullsLast().op("uuid_ops"), table.assignedId.asc().nullsLast().op("uuid_ops")),
	index("tracker_entries_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.assignedId],
			foreignColumns: [users.id],
			name: "tracker_entries_assigned_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "tracker_entries_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "tracker_entries_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Entries can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["authenticated"] }),
	pgPolicy("Entries can be selected by a member of the team", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Entries can be updated by a member of the team", { as: "permissive", for: "update", to: ["authenticated"] }),
	pgPolicy("Entries can be created by a member of the team", { as: "permissive", for: "insert", to: ["authenticated"] }),
]);

export const resources = pgTable("resources", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	teamId: uuid("team_id").notNull(),
	name: text().notNull(),
	type: text().notNull(),
	description: text(),
	serialNumber: text("serial_number"),
	location: text(),
	status: resourceStatus().default('available'),
	purchaseDate: date("purchase_date"),
	purchasePrice: numeric("purchase_price", { precision: 12, scale:  2 }),
	maintenanceSchedule: jsonb("maintenance_schedule").default({}),
	specifications: jsonb().default({}),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_resources_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	index("idx_resources_team").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("idx_resources_type").using("btree", table.type.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "resources_team_id_fkey"
		}),
]);

export const projects = pgTable("projects", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	projectTypeId: uuid("project_type_id"),
	status: text().default('Planning').notNull(),
	startDate: date("start_date"),
	endDate: date("end_date"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_projects_project_type_id").using("btree", table.projectTypeId.asc().nullsLast().op("uuid_ops")),
	index("idx_projects_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.projectTypeId],
			foreignColumns: [projectTypes.id],
			name: "projects_project_type_id_fkey"
		}),
]);

export const roles = pgTable("roles", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("roles_name_key").on(table.name),
]);

export const invoices = pgTable("invoices", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	dueDate: timestamp("due_date", { withTimezone: true, mode: 'string' }),
	invoiceNumber: text("invoice_number"),
	customerId: uuid("customer_id"),
	amount: numeric(),
	currency: text(),
	lineItems: jsonb("line_items"),
	paymentDetails: jsonb("payment_details"),
	customerDetails: jsonb("customer_details"),
	companyDatails: jsonb("company_datails"),
	note: text(),
	internalNote: text("internal_note"),
	teamId: uuid("team_id").notNull(),
	paidAt: timestamp("paid_at", { withTimezone: true, mode: 'string' }),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((COALESCE((amount)::text, ''::text) || ' '::text) || COALESCE(invoice_number, ''::text)))`),
	vat: numeric(),
	tax: numeric(),
	url: text(),
	filePath: text("file_path").array(),
	viewedAt: timestamp("viewed_at", { withTimezone: true, mode: 'string' }),
	fromDetails: jsonb("from_details"),
	issueDate: timestamp("issue_date", { withTimezone: true, mode: 'string' }),
	template: jsonb(),
	noteDetails: jsonb("note_details"),
	customerName: text("customer_name"),
	token: text().notNull(),
	sentTo: text("sent_to"),
	reminderSentAt: timestamp("reminder_sent_at", { withTimezone: true, mode: 'string' }),
	discount: numeric(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fileSize: bigint("file_size", { mode: "number" }),
	userId: uuid("user_id"),
	subtotal: numeric(),
	topBlock: jsonb("top_block"),
	bottomBlock: jsonb("bottom_block"),
	sentAt: timestamp("sent_at", { withTimezone: true, mode: 'string' }),
	scheduledAt: timestamp("scheduled_at", { withTimezone: true, mode: 'string' }),
	scheduledJobId: text("scheduled_job_id"),
	projectId: uuid("project_id"),
}, (table) => [
	index("invoices_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("invoices_fts").using("gin", table.fts.asc().nullsLast().op("tsvector_ops")),
	index("invoices_project_id_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("invoices_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "invoices_created_by_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.customerId],
			foreignColumns: [customers.id],
			name: "invoices_customer_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "invoices_project_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "invoices_team_id_fkey"
		}).onDelete("cascade"),
	unique("invoices_scheduled_job_id_key").on(table.scheduledJobId),
	pgPolicy("Invoices can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const projectTypes = pgTable("project_types", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("project_types_name_key").on(table.name),
]);

export const materials = pgTable("materials", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	teamId: uuid("team_id").notNull(),
	projectId: uuid("project_id").notNull(),
	name: text().notNull(),
	description: text(),
	unit: text().notNull(),
	quantityOrdered: numeric("quantity_ordered", { precision: 12, scale:  2 }).notNull(),
	quantityReceived: numeric("quantity_received", { precision: 12, scale:  2 }).default('0'),
	unitPrice: numeric("unit_price", { precision: 12, scale:  2 }).notNull(),
	supplier: text(),
	deliveryDate: date("delivery_date"),
	specifications: jsonb().default({}),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "materials_project_id_fkey"
		}),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "materials_team_id_fkey"
		}),
]);

export const projectMembers = pgTable("project_members", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id"),
	userId: uuid("user_id"),
	roleId: uuid("role_id"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_members_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_members_role_id").using("btree", table.roleId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_members_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "project_members_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.roleId],
			foreignColumns: [roles.id],
			name: "project_members_role_id_fkey"
		}),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_members_user_id_fkey"
		}).onDelete("cascade"),
	unique("project_members_project_id_user_id_key").on(table.projectId, table.userId),
]);

export const tasks = pgTable("tasks", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id"),
	name: text().notNull(),
	description: text(),
	status: text().default('To-Do').notNull(),
	dueDate: date("due_date"),
	assignedToMemberId: uuid("assigned_to_member_id"),
	parentTaskId: uuid("parent_task_id"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_tasks_assigned_to_member_id").using("btree", table.assignedToMemberId.asc().nullsLast().op("uuid_ops")),
	index("idx_tasks_parent_task_id").using("btree", table.parentTaskId.asc().nullsLast().op("uuid_ops")),
	index("idx_tasks_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_tasks_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.assignedToMemberId],
			foreignColumns: [projectMembers.id],
			name: "tasks_assigned_to_member_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.parentTaskId],
			foreignColumns: [table.id],
			name: "tasks_parent_task_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "tasks_project_id_fkey"
		}).onDelete("cascade"),
]);

export const projectFiles = pgTable("project_files", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	userId: uuid("user_id"),
	name: text().notNull(),
	storagePath: text("storage_path").notNull(),
	fileType: text("file_type"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	size: bigint({ mode: "number" }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_files_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_files_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "project_files_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_files_user_id_fkey"
		}).onDelete("set null"),
	unique("project_files_storage_path_key").on(table.storagePath),
]);

export const customerTags = pgTable("customer_tags", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	customerId: uuid("customer_id").notNull(),
	teamId: uuid("team_id").notNull(),
	tagId: uuid("tag_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.customerId],
			foreignColumns: [customers.id],
			name: "customer_tags_customer_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.tagId],
			foreignColumns: [tags.id],
			name: "customer_tags_tag_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "customer_tags_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_customer_tag").on(table.customerId, table.tagId),
	pgPolicy("Tags can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const transactions = pgTable("transactions", {
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	date: date().notNull(),
	name: text().notNull(),
	method: transactionMethods().notNull(),
	amount: numeric().notNull(),
	currency: text().notNull(),
	teamId: uuid("team_id").notNull(),
	assignedId: uuid("assigned_id"),
	note: varchar(),
	bankAccountId: uuid("bank_account_id"),
	id: uuid().defaultRandom().primaryKey().notNull(),
	internalId: text("internal_id").notNull(),
	status: transactionStatus().default('posted'),
	category: transactionCategories(),
	balance: numeric(),
	manual: boolean().default(false),
	description: text(),
	categorySlug: text("category_slug"),
	baseAmount: numeric("base_amount"),
	baseCurrency: text("base_currency"),
	recurring: boolean(),
	frequency: transactionFrequency(),
	// TODO: failed to parse database type 'tsvector'
	ftsVector: unknown("fts_vector").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((COALESCE(name, ''::text) || ' '::text) || COALESCE(description, ''::text)))`),
	notified: boolean().default(false),
	internal: boolean().default(false),
}, (table) => [
	index("idx_transactions_date").using("btree", table.date.asc().nullsLast().op("date_ops")),
	index("idx_transactions_fts").using("gin", table.ftsVector.asc().nullsLast().op("tsvector_ops")),
	index("idx_transactions_fts_vector").using("gin", table.ftsVector.asc().nullsLast().op("tsvector_ops")),
	index("idx_transactions_id").using("btree", table.id.asc().nullsLast().op("uuid_ops")),
	index("idx_transactions_name").using("btree", table.name.asc().nullsLast().op("text_ops")),
	index("idx_transactions_name_trigram").using("gin", table.name.asc().nullsLast().op("gin_trgm_ops")),
	index("idx_trgm_name").using("gist", table.name.asc().nullsLast().op("gist_trgm_ops")),
	index("transactions_assigned_id_idx").using("btree", table.assignedId.asc().nullsLast().op("uuid_ops")),
	index("transactions_bank_account_id_idx").using("btree", table.bankAccountId.asc().nullsLast().op("uuid_ops")),
	index("transactions_category_slug_idx").using("btree", table.categorySlug.asc().nullsLast().op("text_ops")),
	index("transactions_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.assignedId],
			foreignColumns: [users.id],
			name: "public_transactions_assigned_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "public_transactions_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.bankAccountId],
			foreignColumns: [bankAccounts.id],
			name: "transactions_bank_account_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId, table.categorySlug],
			foreignColumns: [transactionCategories.teamId, transactionCategories.slug],
			name: "transactions_category_slug_team_id_fkey"
		}),
	unique("transactions_internal_id_key").on(table.internalId),
	pgPolicy("Transactions can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Transactions can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Transactions can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Transactions can be created by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
]);

export const inboxAccounts = pgTable("inbox_accounts", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	email: text().notNull(),
	accessToken: text("access_token").notNull(),
	refreshToken: text("refresh_token").notNull(),
	teamId: uuid("team_id").notNull(),
	lastAccessed: timestamp("last_accessed", { withTimezone: true, mode: 'string' }).notNull(),
	provider: inboxAccountProviders().notNull(),
	externalId: text("external_id").notNull(),
	expiryDate: timestamp("expiry_date", { withTimezone: true, mode: 'string' }).notNull(),
	scheduleId: text("schedule_id"),
}, (table) => [
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "inbox_accounts_team_id_fkey"
		}).onDelete("cascade"),
	unique("inbox_accounts_email_key").on(table.email),
	unique("inbox_accounts_external_id_key").on(table.externalId),
	pgPolicy("Inbox accounts can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Inbox accounts can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Inbox accounts can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
]);

export const resourceAllocations = pgTable("resource_allocations", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	resourceId: uuid("resource_id").notNull(),
	projectId: uuid("project_id").notNull(),
	teamId: uuid("team_id").notNull(),
	startDate: date("start_date").notNull(),
	endDate: date("end_date").notNull(),
	notes: text(),
	assignedBy: text("assigned_by").notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "resource_allocations_project_id_fkey"
		}),
	foreignKey({
			columns: [table.resourceId],
			foreignColumns: [resources.id],
			name: "resource_allocations_resource_id_fkey"
		}),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "resource_allocations_team_id_fkey"
		}),
]);

export const customers = pgTable("customers", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	country: text(),
	addressLine1: text("address_line_1"),
	addressLine2: text("address_line_2"),
	city: text(),
	state: text(),
	zip: text(),
	note: text(),
	teamId: uuid("team_id").defaultRandom().notNull(),
	website: text(),
	phone: text(),
	vatNumber: text("vat_number"),
	countryCode: text("country_code"),
	token: text().notNull(),
	contact: text(),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((((((((((((((((((COALESCE(name, ''::text) || ' '::text) || COALESCE(contact, ''::text)) || ' '::text) || COALESCE(phone, ''::text)) || ' '::text) || COALESCE(email, ''::text)) || ' '::text) || COALESCE(address_line_1, ''::text)) || ' '::text) || COALESCE(address_line_2, ''::text)) || ' '::text) || COALESCE(city, ''::text)) || ' '::text) || COALESCE(state, ''::text)) || ' '::text) || COALESCE(zip, ''::text)) || ' '::text) || COALESCE(country, ''::text)))`),
}, (table) => [
	index("customers_fts").using("gin", table.fts.asc().nullsLast().op("tsvector_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "customers_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Customers can be handled by members of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const documentTags = pgTable("document_tags", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	name: text().notNull(),
	slug: text().notNull(),
	teamId: uuid("team_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "document_tags_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_slug_per_team").on(table.slug, table.teamId),
	pgPolicy("Tags can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const exchangeRates = pgTable("exchange_rates", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	base: text(),
	rate: numeric(),
	target: text(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("exchange_rates_base_target_idx").using("btree", table.base.asc().nullsLast().op("text_ops"), table.target.asc().nullsLast().op("text_ops")),
	unique("unique_rate").on(table.base, table.target),
	pgPolicy("Enable read access for authenticated users", { as: "permissive", for: "select", to: ["public"], using: sql`true` }),
]);

export const invoiceComments = pgTable("invoice_comments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
});

export const trackerReports = pgTable("tracker_reports", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	linkId: text("link_id"),
	shortLink: text("short_link"),
	teamId: uuid("team_id").defaultRandom(),
	projectId: uuid("project_id").defaultRandom(),
	createdBy: uuid("created_by"),
}, (table) => [
	index("tracker_reports_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "public_tracker_reports_created_by_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "public_tracker_reports_project_id_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "tracker_reports_team_id_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	pgPolicy("Reports can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const bankConnections = pgTable("bank_connections", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	institutionId: text("institution_id").notNull(),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }),
	teamId: uuid("team_id").notNull(),
	name: text().notNull(),
	logoUrl: text("logo_url"),
	accessToken: text("access_token"),
	enrollmentId: text("enrollment_id"),
	provider: bankProviders(),
	lastAccessed: timestamp("last_accessed", { withTimezone: true, mode: 'string' }),
	referenceId: text("reference_id"),
	errorDetails: text("error_details"),
	errorRetries: smallint("error_retries").default(sql`'0'`),
}, (table) => [
	index("bank_connections_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("idx_bank_connections_team_id").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("idx_bank_connections_team_id_new").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "bank_connections_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_bank_connections").on(table.institutionId, table.teamId),
	pgPolicy("Bank Connections can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
	pgPolicy("Bank Connections can be inserted by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Bank Connections can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Bank Connections can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Bank Connections can be created by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
]);

export const reports = pgTable("reports", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	linkId: text("link_id"),
	teamId: uuid("team_id"),
	shortLink: text("short_link"),
	from: timestamp({ withTimezone: true, mode: 'string' }),
	to: timestamp({ withTimezone: true, mode: 'string' }),
	type: reportTypes(),
	expireAt: timestamp("expire_at", { withTimezone: true, mode: 'string' }),
	currency: text(),
	createdBy: uuid("created_by"),
}, (table) => [
	index("reports_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "public_reports_created_by_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "reports_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Reports can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Reports can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Reports can be updated by member of team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Reports can be created by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
]);

export const users = pgTable("users", {
	id: uuid().primaryKey().notNull(),
	fullName: text("full_name"),
	avatarUrl: text("avatar_url"),
	email: text(),
	teamId: uuid("team_id"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	locale: text().default('en'),
	weekStartsOnMonday: boolean("week_starts_on_monday").default(false),
	timezone: text(),
	timeFormat: numeric("time_format").default('24'),
	dateFormat: text("date_format"),
}, (table) => [
	index("users_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.id],
			foreignColumns: [table.id],
			name: "users_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "users_team_id_fkey"
		}).onDelete("set null"),
	pgPolicy("Users can insert their own profile.", { as: "permissive", for: "insert", to: ["public"], withCheck: sql`(auth.uid() = id)`  }),
	pgPolicy("Users can select their own profile.", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Users can select users if they are in the same team", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Users can update own profile.", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Users can select their own profile", { as: "permissive", for: "select", to: ["public"] }),
]);

export const transactionEnrichments = pgTable("transaction_enrichments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	name: text(),
	teamId: uuid("team_id"),
	categorySlug: text("category_slug"),
	system: boolean().default(false),
}, (table) => [
	index("transaction_enrichments_category_slug_team_id_idx").using("btree", table.categorySlug.asc().nullsLast().op("text_ops"), table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId, table.categorySlug],
			foreignColumns: [transactionCategories.teamId, transactionCategories.slug],
			name: "transaction_enrichments_category_slug_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "transaction_enrichments_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_team_name").on(table.name, table.teamId),
	pgPolicy("Enable insert for authenticated users only", { as: "permissive", for: "insert", to: ["authenticated"], withCheck: sql`true`  }),
	pgPolicy("Enable update for authenticated users only", { as: "permissive", for: "update", to: ["authenticated"] }),
]);

export const invoiceTemplates = pgTable("invoice_templates", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	teamId: uuid("team_id").notNull(),
	customerLabel: text("customer_label"),
	fromLabel: text("from_label"),
	invoiceNoLabel: text("invoice_no_label"),
	issueDateLabel: text("issue_date_label"),
	dueDateLabel: text("due_date_label"),
	descriptionLabel: text("description_label"),
	priceLabel: text("price_label"),
	quantityLabel: text("quantity_label"),
	totalLabel: text("total_label"),
	vatLabel: text("vat_label"),
	taxLabel: text("tax_label"),
	paymentLabel: text("payment_label"),
	noteLabel: text("note_label"),
	logoUrl: text("logo_url"),
	currency: text(),
	paymentDetails: jsonb("payment_details"),
	fromDetails: jsonb("from_details"),
	size: invoiceSize().default('a4'),
	dateFormat: text("date_format"),
	includeVat: boolean("include_vat"),
	includeTax: boolean("include_tax"),
	taxRate: numeric("tax_rate"),
	deliveryType: invoiceDeliveryType("delivery_type").default('create').notNull(),
	discountLabel: text("discount_label"),
	includeDiscount: boolean("include_discount"),
	includeDecimals: boolean("include_decimals"),
	includeQr: boolean("include_qr"),
	totalSummaryLabel: text("total_summary_label"),
	title: text(),
	vatRate: numeric("vat_rate"),
	includeUnits: boolean("include_units"),
	subtotalLabel: text("subtotal_label"),
	includePdf: boolean("include_pdf"),
	sendCopy: boolean("send_copy"),
}, (table) => [
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "invoice_settings_team_id_fkey"
		}).onDelete("cascade"),
	unique("invoice_templates_team_id_key").on(table.teamId),
	pgPolicy("Invoice templates can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const projectTrades = pgTable("project_trades", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	tradeType: text("trade_type").notNull(),
	displayName: text("display_name").notNull(),
	assignedTeamId: uuid("assigned_team_id"),
	foremanId: uuid("foreman_id"),
	status: text().default('pending'),
	startDate: date("start_date"),
	endDate: date("end_date"),
	budget: numeric({ precision: 12, scale:  2 }),
	actualCost: numeric("actual_cost", { precision: 12, scale:  2 }),
	completionPercentage: numeric("completion_percentage", { precision: 5, scale:  2 }).default('0'),
	requirements: jsonb(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_trades_foreman").using("btree", table.foremanId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_trades_project").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_trades_team").using("btree", table.assignedTeamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.assignedTeamId],
			foreignColumns: [teams.id],
			name: "project_trades_assigned_team_id_fkey"
		}),
	foreignKey({
			columns: [table.foremanId],
			foreignColumns: [users.id],
			name: "project_trades_foreman_id_fkey"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_trades_project_id_fkey"
		}).onDelete("cascade"),
]);

export const bankAccounts = pgTable("bank_accounts", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	createdBy: uuid("created_by").notNull(),
	teamId: uuid("team_id").notNull(),
	name: text(),
	currency: text(),
	bankConnectionId: uuid("bank_connection_id"),
	enabled: boolean().default(true).notNull(),
	accountId: text("account_id").notNull(),
	balance: numeric().default('0'),
	manual: boolean().default(false),
	baseCurrency: text("base_currency"),
	baseBalance: numeric("base_balance"),
	errorDetails: text("error_details"),
	errorRetries: smallint("error_retries"),
	accountReference: text("account_reference"),
	type: accountType(),
}, (table) => [
	index("bank_accounts_bank_connection_id_idx").using("btree", table.bankConnectionId.asc().nullsLast().op("uuid_ops")),
	index("bank_accounts_created_by_idx").using("btree", table.createdBy.asc().nullsLast().op("uuid_ops")),
	index("bank_accounts_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("idx_bank_accounts_bank_connection_id").using("btree", table.bankConnectionId.asc().nullsLast().op("uuid_ops")).where(sql`(bank_connection_id IS NOT NULL)`),
	index("idx_bank_accounts_currency").using("btree", table.currency.asc().nullsLast().op("text_ops")),
	index("idx_bank_accounts_enabled_team").using("btree", table.teamId.asc().nullsLast().op("uuid_ops"), table.enabled.asc().nullsLast().op("uuid_ops")).where(sql`(enabled = true)`),
	index("idx_bank_accounts_team_enabled").using("btree", table.teamId.asc().nullsLast().op("bool_ops"), table.enabled.asc().nullsLast().op("uuid_ops")).where(sql`(enabled = true)`),
	index("idx_bank_accounts_team_id_enabled").using("btree", table.teamId.asc().nullsLast().op("bool_ops"), table.enabled.asc().nullsLast().op("bool_ops")),
	foreignKey({
			columns: [table.bankConnectionId],
			foreignColumns: [bankConnections.id],
			name: "bank_accounts_bank_connection_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "bank_accounts_created_by_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "public_bank_accounts_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Bank Accounts can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
	pgPolicy("Bank Accounts can be inserted by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Bank Accounts can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Bank Accounts can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Bank Accounts can be created by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
]);

export const apps = pgTable("apps", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	teamId: uuid("team_id").defaultRandom(),
	config: jsonb(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	appId: text("app_id").notNull(),
	createdBy: uuid("created_by").defaultRandom(),
	settings: jsonb(),
}, (table) => [
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "apps_created_by_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "integrations_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_app_id_team_id").on(table.teamId, table.appId),
	pgPolicy("Apps can be inserted by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("Apps can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Apps can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Apps can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
]);

export const documents = pgTable("documents", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	metadata: jsonb(),
	pathTokens: text("path_tokens").array(),
	teamId: uuid("team_id"),
	parentId: text("parent_id"),
	objectId: uuid("object_id"),
	ownerId: uuid("owner_id"),
	tag: text(),
	title: text(),
	body: text(),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((title || ' '::text) || body))`),
	summary: text(),
	content: text(),
	date: date(),
	language: text(),
	processingStatus: documentProcessingStatus("processing_status").default('pending'),
	// TODO: failed to parse database type 'tsvector'
	ftsSimple: unknown("fts_simple"),
	// TODO: failed to parse database type 'tsvector'
	ftsEnglish: unknown("fts_english"),
	// TODO: failed to parse database type 'tsvector'
	ftsLanguage: unknown("fts_language"),
}, (table) => [
	index("documents_name_idx").using("btree", table.name.asc().nullsLast().op("text_ops")),
	index("documents_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("idx_documents_fts_english").using("gin", table.ftsEnglish.asc().nullsLast().op("tsvector_ops")),
	index("idx_documents_fts_language").using("gin", table.ftsLanguage.asc().nullsLast().op("tsvector_ops")),
	index("idx_documents_fts_simple").using("gin", table.ftsSimple.asc().nullsLast().op("tsvector_ops")),
	index("idx_gin_documents_title").using("gin", table.title.asc().nullsLast().op("gin_trgm_ops")),
	foreignKey({
			columns: [table.ownerId],
			foreignColumns: [users.id],
			name: "documents_created_by_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "storage_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Documents can be selected by a member of the team", { as: "permissive", for: "all", to: ["public"] }),
	pgPolicy("Documents can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Enable insert for authenticated users only", { as: "permissive", for: "insert", to: ["authenticated"] }),
	pgPolicy("Documents can be deleted by a member of the team", { as: "permissive", for: "all", to: ["public"] }),
]);

export const projectModels = pgTable("project_models", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	name: text().notNull(),
	filePath: text("file_path").array().notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fileSize: bigint("file_size", { mode: "number" }),
	fileType: text("file_type").notNull(),
	version: integer().default(1),
	uploadedBy: uuid("uploaded_by").notNull(),
	uploadedAt: timestamp("uploaded_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	modelMetadata: jsonb("model_metadata"),
	thumbnailPath: text("thumbnail_path"),
	processingStatus: text("processing_status").default('pending'),
	viewerData: jsonb("viewer_data"),
	layers: jsonb(),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_models_project").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_models_status").using("btree", table.processingStatus.asc().nullsLast().op("text_ops")),
	index("idx_project_models_type").using("btree", table.fileType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_models_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.uploadedBy],
			foreignColumns: [users.id],
			name: "project_models_uploaded_by_fkey"
		}),
]);

export const modelAnnotations = pgTable("model_annotations", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	modelId: uuid("model_id").notNull(),
	userId: uuid("user_id").notNull(),
	annotationType: text("annotation_type").notNull(),
	position: jsonb().notNull(),
	cameraPosition: jsonb("camera_position"),
	content: text(),
	attachments: text().array(),
	status: text().default('open'),
	priority: text().default('normal'),
	assignedTo: uuid("assigned_to"),
	resolvedBy: uuid("resolved_by"),
	resolvedAt: timestamp("resolved_at", { withTimezone: true, mode: 'string' }),
	dueDate: timestamp("due_date", { withTimezone: true, mode: 'string' }),
	tags: text().array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_model_annotations_model").using("btree", table.modelId.asc().nullsLast().op("uuid_ops")),
	index("idx_model_annotations_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("idx_model_annotations_type").using("btree", table.annotationType.asc().nullsLast().op("text_ops")),
	index("idx_model_annotations_user").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	index("model_annotations_model_id_idx").using("btree", table.modelId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.assignedTo],
			foreignColumns: [users.id],
			name: "model_annotations_assigned_to_fkey"
		}),
	foreignKey({
			columns: [table.modelId],
			foreignColumns: [projectModels.id],
			name: "model_annotations_model_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.resolvedBy],
			foreignColumns: [users.id],
			name: "model_annotations_resolved_by_fkey"
		}),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "model_annotations_user_id_fkey"
		}),
]);

export const annotationThreads = pgTable("annotation_threads", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	annotationId: uuid("annotation_id").notNull(),
	userId: uuid("user_id").notNull(),
	content: text().notNull(),
	attachments: text().array(),
	mentionedUsers: uuid("mentioned_users").array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_annotation_threads_annotation").using("btree", table.annotationId.asc().nullsLast().op("uuid_ops")),
	index("idx_annotation_threads_user").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.annotationId],
			foreignColumns: [modelAnnotations.id],
			name: "annotation_threads_annotation_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "annotation_threads_user_id_fkey"
		}),
]);

export const transactionAttachments = pgTable("transaction_attachments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	type: text(),
	transactionId: uuid("transaction_id"),
	teamId: uuid("team_id"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	size: bigint({ mode: "number" }),
	name: text(),
	path: text().array(),
}, (table) => [
	index("transaction_attachments_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("transaction_attachments_transaction_id_idx").using("btree", table.transactionId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "public_transaction_attachments_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.transactionId],
			foreignColumns: [transactions.id],
			name: "public_transaction_attachments_transaction_id_fkey"
		}).onDelete("set null"),
	pgPolicy("Transaction Attachments can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Transaction Attachments can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Transaction Attachments can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
	pgPolicy("Transaction Attachments can be created by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
]);

export const trackerProjectTags = pgTable("tracker_project_tags", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	trackerProjectId: uuid("tracker_project_id").notNull(),
	tagId: uuid("tag_id").notNull(),
	teamId: uuid("team_id").notNull(),
}, (table) => [
	index("tracker_project_tags_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("tracker_project_tags_tracker_project_id_tag_id_team_id_idx").using("btree", table.trackerProjectId.asc().nullsLast().op("uuid_ops"), table.tagId.asc().nullsLast().op("uuid_ops"), table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.tagId],
			foreignColumns: [tags.id],
			name: "project_tags_tag_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.trackerProjectId],
			foreignColumns: [trackerProjects.id],
			name: "project_tags_tracker_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "tracker_project_tags_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_project_tag").on(table.trackerProjectId, table.tagId),
	pgPolicy("Tags can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const transactionTags = pgTable("transaction_tags", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	teamId: uuid("team_id").notNull(),
	tagId: uuid("tag_id").notNull(),
	transactionId: uuid("transaction_id").notNull(),
}, (table) => [
	index("transaction_tags_tag_id_idx").using("btree", table.tagId.asc().nullsLast().op("uuid_ops")),
	index("transaction_tags_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("transaction_tags_transaction_id_tag_id_team_id_idx").using("btree", table.transactionId.asc().nullsLast().op("uuid_ops"), table.tagId.asc().nullsLast().op("uuid_ops"), table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.tagId],
			foreignColumns: [tags.id],
			name: "transaction_tags_tag_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "transaction_tags_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.transactionId],
			foreignColumns: [transactions.id],
			name: "transaction_tags_transaction_id_fkey"
		}).onDelete("cascade"),
	unique("unique_tag").on(table.tagId, table.transactionId),
	pgPolicy("Transaction Tags can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const documentTagEmbeddings = pgTable("document_tag_embeddings", {
	slug: text().primaryKey().notNull(),
	embedding: vector({ dimensions: 1024 }),
	name: text().notNull(),
}, (table) => [
	index("document_tag_embeddings_idx").using("ivfflat", table.embedding.asc().nullsLast().op("vector_l2_ops")).with({lists: "100"}),
	pgPolicy("Enable insert for authenticated users only", { as: "permissive", for: "insert", to: ["authenticated"], withCheck: sql`true`  }),
]);

export const apiKeys = pgTable("api_keys", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	keyEncrypted: text("key_encrypted").notNull(),
	name: text().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	userId: uuid("user_id").notNull(),
	teamId: uuid("team_id").notNull(),
	keyHash: text("key_hash"),
	scopes: text().array().default([""]).notNull(),
	lastUsedAt: timestamp("last_used_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("api_keys_key_idx").using("btree", table.keyHash.asc().nullsLast().op("text_ops")),
	index("api_keys_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("api_keys_user_id_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "api_keys_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "api_keys_user_id_fkey"
		}).onDelete("cascade"),
	unique("api_keys_key_unique").on(table.keyHash),
]);

export const projectTasks = pgTable("project_tasks", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	phaseId: uuid("phase_id"),
	parentTaskId: uuid("parent_task_id"),
	name: text().notNull(),
	description: text(),
	taskOrder: integer("task_order"),
	status: taskStatus().default('not_started'),
	priority: projectPriority().default('medium'),
	estimatedHours: numeric("estimated_hours", { precision: 8, scale:  2 }),
	actualHours: numeric("actual_hours", { precision: 8, scale:  2 }).default('0'),
	estimatedCost: numeric("estimated_cost", { precision: 12, scale:  2 }),
	actualCost: numeric("actual_cost", { precision: 12, scale:  2 }).default('0'),
	startDate: date("start_date"),
	endDate: date("end_date"),
	estimatedStart: date("estimated_start"),
	estimatedEnd: date("estimated_end"),
	completionPercentage: numeric("completion_percentage", { precision: 5, scale:  2 }).default('0'),
	dependencies: uuid().array(),
	assignedTo: uuid("assigned_to"),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((COALESCE(name, ''::text) || ' '::text) || COALESCE(description, ''::text)))`),
}, (table) => [
	index("idx_project_tasks_assigned_to").using("btree", table.assignedTo.asc().nullsLast().op("uuid_ops")),
	index("idx_project_tasks_fts").using("gin", table.fts.asc().nullsLast().op("tsvector_ops")),
	index("idx_project_tasks_phase_id").using("btree", table.phaseId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_tasks_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_tasks_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.assignedTo],
			foreignColumns: [users.id],
			name: "project_tasks_assigned_to_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "project_tasks_created_by_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.parentTaskId],
			foreignColumns: [table.id],
			name: "project_tasks_parent_task_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.phaseId],
			foreignColumns: [projectPhases.id],
			name: "project_tasks_phase_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_tasks_project_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("project_tasks_team_access", { as: "permissive", for: "all", to: ["authenticated"], using: sql`(project_id IN ( SELECT ptm.project_id
   FROM project_team_members ptm
  WHERE ((ptm.user_id = auth.uid()) AND (ptm.is_active = true))))` }),
]);

export const bimModels = pgTable("bim_models", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	documentId: uuid("document_id").notNull(),
	projectId: uuid("project_id"),
	teamId: uuid("team_id").notNull(),
	name: text().notNull(),
	fileFormat: text("file_format").notNull(),
	processingStatus: text("processing_status").default('pending').notNull(),
	metadata: jsonb(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("bim_models_project_id_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("bim_models_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.documentId],
			foreignColumns: [documents.id],
			name: "bim_models_document_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "bim_models_project_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "bim_models_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("BIM models can be handled by team members", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const projectTeamMembers = pgTable("project_team_members", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	userId: uuid("user_id").notNull(),
	hourlyRate: numeric("hourly_rate", { precision: 8, scale:  2 }),
	startDate: date("start_date"),
	endDate: date("end_date"),
	isActive: boolean("is_active").default(true),
	permissions: jsonb(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_team_members_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_team_members_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_team_members_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_team_members_user_id_fkey"
		}).onDelete("cascade"),
]);

export const projectResources = pgTable("project_resources", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	taskId: uuid("task_id"),
	resourceType: resourceType("resource_type").notNull(),
	name: text().notNull(),
	description: text(),
	quantity: numeric({ precision: 10, scale:  2 }).default('1').notNull(),
	unit: text(),
	unitCost: numeric("unit_cost", { precision: 10, scale:  2 }),
	totalCost: numeric("total_cost", { precision: 12, scale:  2 }),
	vendorContact: text("vendor_contact"),
	availabilityStart: date("availability_start"),
	availabilityEnd: date("availability_end"),
	allocated: boolean().default(false),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_resources_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_resources_task_id").using("btree", table.taskId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_resources_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "project_resources_task_id_fkey"
		}).onDelete("set null"),
	pgPolicy("project_resources_team_access", { as: "permissive", for: "all", to: ["authenticated"], using: sql`(project_id IN ( SELECT ptm.project_id
   FROM project_team_members ptm
  WHERE ((ptm.user_id = auth.uid()) AND (ptm.is_active = true))))` }),
]);

export const projectPhases = pgTable("project_phases", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	name: text().notNull(),
	description: text(),
	phaseOrder: integer("phase_order").notNull(),
	startDate: date("start_date"),
	endDate: date("end_date"),
	estimatedStart: date("estimated_start"),
	estimatedEnd: date("estimated_end"),
	status: taskStatus().default('not_started'),
	completionPercentage: numeric("completion_percentage", { precision: 5, scale:  2 }).default('0'),
	dependencies: uuid().array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_phases_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("idx_project_phases_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_phases_project_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("project_phases_team_access", { as: "permissive", for: "all", to: ["authenticated"], using: sql`(project_id IN ( SELECT ptm.project_id
   FROM project_team_members ptm
  WHERE ((ptm.user_id = auth.uid()) AND (ptm.is_active = true))))` }),
]);

export const projectTemplates = pgTable("project_templates", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	projectType: projectType("project_type").notNull(),
	teamId: uuid("team_id").notNull(),
	estimatedDurationDays: integer("estimated_duration_days"),
	templateData: jsonb("template_data"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	createdBy: uuid("created_by"),
}, (table) => [
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "project_templates_created_by_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "project_templates_team_id_fkey"
		}).onDelete("cascade"),
]);

export const trackerProjects = pgTable("tracker_projects", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	teamId: uuid("team_id"),
	rate: numeric(),
	currency: text(),
	status: trackerStatus().default('in_progress').notNull(),
	description: text(),
	name: text().notNull(),
	billable: boolean().default(false),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	estimate: bigint({ mode: "number" }),
	customerId: uuid("customer_id"),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((COALESCE(name, ''::text) || ' '::text) || COALESCE(description, ''::text)))`),
	projectType: text("project_type").default('general'),
	parentProjectId: uuid("parent_project_id"),
	address: text(),
	startDate: date("start_date"),
	endDate: date("end_date"),
	budgetAllocated: numeric("budget_allocated", { precision: 12, scale:  2 }),
	constructionPhase: constructionPhase("construction_phase"),
	location: jsonb(),
	safetyRequirements: jsonb("safety_requirements"),
	permitNumbers: text("permit_numbers").array(),
	siteConditions: jsonb("site_conditions"),
	weatherDelays: integer("weather_delays").default(0),
	completionPercentage: numeric("completion_percentage", { precision: 5, scale:  2 }).default('0'),
	priority: projectPriority().default('medium'),
	plannedStartDate: date("planned_start_date"),
	plannedEndDate: date("planned_end_date"),
	actualStartDate: date("actual_start_date"),
	actualEndDate: date("actual_end_date"),
	budget: numeric({ precision: 15, scale:  2 }),
	actualCost: numeric("actual_cost", { precision: 15, scale:  2 }).default('0'),
	projectManagerId: uuid("project_manager_id"),
	clientContactInfo: jsonb("client_contact_info"),
	locationAddress: text("location_address"),
	locationCoordinates: point("location_coordinates"),
	weatherDependent: boolean("weather_dependent").default(false),
	permitRequired: boolean("permit_required").default(false),
	permitStatus: text("permit_status").default('not_required'),
	templateId: uuid("template_id"),
}, (table) => [
	index("idx_tracker_projects_parent").using("btree", table.parentProjectId.asc().nullsLast().op("uuid_ops")),
	index("idx_tracker_projects_phase").using("btree", table.constructionPhase.asc().nullsLast().op("enum_ops")),
	index("idx_tracker_projects_project_manager").using("btree", table.projectManagerId.asc().nullsLast().op("uuid_ops")),
	index("idx_tracker_projects_team_status").using("btree", table.teamId.asc().nullsLast().op("uuid_ops"), table.status.asc().nullsLast().op("enum_ops")),
	index("idx_tracker_projects_type").using("btree", table.projectType.asc().nullsLast().op("text_ops")),
	index("idx_tracker_projects_type_status").using("btree", table.projectType.asc().nullsLast().op("enum_ops"), table.status.asc().nullsLast().op("enum_ops")),
	index("tracker_projects_fts").using("gin", table.fts.asc().nullsLast().op("tsvector_ops")),
	index("tracker_projects_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.customerId],
			foreignColumns: [customers.id],
			name: "tracker_projects_customer_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.parentProjectId],
			foreignColumns: [table.id],
			name: "tracker_projects_parent_project_id_fkey"
		}),
	foreignKey({
			columns: [table.projectManagerId],
			foreignColumns: [users.id],
			name: "tracker_projects_project_manager_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "tracker_projects_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.templateId],
			foreignColumns: [projectTemplates.id],
			name: "tracker_projects_template_id_fkey"
		}).onDelete("set null"),
	pgPolicy("Projects can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["authenticated"] }),
	pgPolicy("Projects can be selected by a member of the team", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Projects can be updated by a member of the team", { as: "permissive", for: "update", to: ["authenticated"] }),
	pgPolicy("Projects can be created by a member of the team", { as: "permissive", for: "insert", to: ["authenticated"] }),
]);

export const projectCommunications = pgTable("project_communications", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	senderId: uuid("sender_id"),
	recipients: uuid().array().notNull(),
	communicationType: text("communication_type").default('message'),
	subject: text(),
	content: text().notNull(),
	priority: projectPriority().default('medium'),
	readBy: jsonb("read_by").default({}),
	attachments: uuid().array(),
	threadId: uuid("thread_id"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").generatedAlwaysAs(sql`to_tsvector('english'::regconfig, ((COALESCE(subject, ''::text) || ' '::text) || COALESCE(content, ''::text)))`),
}, (table) => [
	index("idx_project_communications_fts").using("gin", table.fts.asc().nullsLast().op("tsvector_ops")),
	index("idx_project_communications_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_communications_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.senderId],
			foreignColumns: [users.id],
			name: "project_communications_sender_id_fkey"
		}).onDelete("set null"),
]);

export const projectInspections = pgTable("project_inspections", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	taskId: uuid("task_id"),
	phaseId: uuid("phase_id"),
	inspectorId: uuid("inspector_id"),
	inspectionType: text("inspection_type").notNull(),
	scheduledDate: date("scheduled_date"),
	completedDate: date("completed_date"),
	status: text().default('scheduled'),
	passed: boolean(),
	notes: text(),
	checklistItems: jsonb("checklist_items"),
	attachments: uuid().array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_inspections_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.inspectorId],
			foreignColumns: [users.id],
			name: "project_inspections_inspector_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.phaseId],
			foreignColumns: [projectPhases.id],
			name: "project_inspections_phase_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_inspections_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "project_inspections_task_id_fkey"
		}).onDelete("set null"),
]);

export const safetyIncidents = pgTable("safety_incidents", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	reportedBy: uuid("reported_by"),
	incidentDate: date("incident_date").notNull(),
	incidentType: text("incident_type").notNull(),
	severity: text().notNull(),
	description: text().notNull(),
	location: text(),
	injuredPerson: text("injured_person"),
	witnesses: text().array(),
	immediateAction: text("immediate_action"),
	rootCause: text("root_cause"),
	preventiveMeasures: text("preventive_measures"),
	status: text().default('open'),
	attachments: uuid().array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_safety_incidents_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "safety_incidents_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.reportedBy],
			foreignColumns: [users.id],
			name: "safety_incidents_reported_by_fkey"
		}).onDelete("set null"),
]);

export const projectProgressLogs = pgTable("project_progress_logs", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	taskId: uuid("task_id"),
	phaseId: uuid("phase_id"),
	loggedBy: uuid("logged_by"),
	progressDate: date("progress_date").default(sql`CURRENT_DATE`),
	progressPercentage: numeric("progress_percentage", { precision: 5, scale:  2 }),
	hoursWorked: numeric("hours_worked", { precision: 8, scale:  2 }),
	workDescription: text("work_description"),
	issuesEncountered: text("issues_encountered"),
	photos: uuid().array(),
	weatherConditions: text("weather_conditions"),
	location: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_project_progress_logs_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.loggedBy],
			foreignColumns: [users.id],
			name: "project_progress_logs_logged_by_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.phaseId],
			foreignColumns: [projectPhases.id],
			name: "project_progress_logs_phase_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "project_progress_logs_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "project_progress_logs_task_id_fkey"
		}).onDelete("set null"),
]);

export const changeOrders = pgTable("change_orders", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid("project_id").notNull(),
	changeOrderNumber: text("change_order_number").notNull(),
	title: text().notNull(),
	description: text().notNull(),
	reason: text(),
	requestedBy: uuid("requested_by"),
	approvedBy: uuid("approved_by"),
	costImpact: numeric("cost_impact", { precision: 12, scale:  2 }).default('0'),
	timeImpactDays: integer("time_impact_days").default(0),
	status: text().default('pending'),
	priority: projectPriority().default('medium'),
	requestDate: date("request_date").default(sql`CURRENT_DATE`),
	approvalDate: date("approval_date"),
	implementationDate: date("implementation_date"),
	attachments: uuid().array(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_change_orders_project_id").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.approvedBy],
			foreignColumns: [users.id],
			name: "change_orders_approved_by_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [trackerProjects.id],
			name: "change_orders_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.requestedBy],
			foreignColumns: [users.id],
			name: "change_orders_requested_by_fkey"
		}).onDelete("set null"),
]);

export const constructionProgressUpdates = pgTable("construction_progress_updates", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	projectId: uuid("project_id").notNull(),
	teamId: uuid("team_id").notNull(),
	userId: uuid("user_id").notNull(),
	phase: constructionPhase(),
	progressPercentage: numeric({ precision: 5, scale:  2 }).notNull(),
	description: text(),
	photos: text().array(),
	notes: text(),
	workCompleted: text("work_completed"),
	nextSteps: text("next_steps"),
	weatherConditions: text("weather_conditions"),
	workersOnSite: smallint("workers_on_site"),
	equipmentUsed: jsonb("equipment_used"),
	issuesReported: text("issues_reported"),
}, (table) => [
	index("construction_progress_updates_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("construction_progress_updates_project_id_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [constructionProjects.id],
			name: "construction_progress_updates_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "construction_progress_updates_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "construction_progress_updates_user_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Progress updates can be created by a member of the team", { as: "permissive", for: "insert", to: ["authenticated"], withCheck: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))`  }),
	pgPolicy("Progress updates can be selected by a member of the team", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Progress updates can be updated by creator", { as: "permissive", for: "update", to: ["authenticated"] }),
]);

export const constructionProjects = pgTable("construction_projects", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	teamId: uuid("team_id").notNull(),
	name: text().notNull(),
	description: text(),
	status: constructionProjectStatus().default('planning').notNull(),
	customerId: uuid("customer_id"),
	location: text(),
	address: text(),
	startDate: timestamp("start_date", { withTimezone: true, mode: 'string' }),
	endDate: timestamp("end_date", { withTimezone: true, mode: 'string' }),
	estimatedCost: numeric({ precision: 12, scale:  2 }),
	actualCost: numeric({ precision: 12, scale:  2 }),
	currency: text(),
	completionPercentage: numeric({ precision: 5, scale:  2 }).default('0'),
	siteArea: numeric({ precision: 10, scale:  2 }),
	buildingArea: numeric({ precision: 10, scale:  2 }),
	currentPhase: constructionPhase(),
	contractorInfo: jsonb("contractor_info"),
	permitInfo: jsonb("permit_info"),
	siteCoordinates: jsonb("site_coordinates"),
	projectFiles: text("project_files").array(),
	createdBy: uuid("created_by").notNull(),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").notNull().generatedAlwaysAs(sql`to_tsvector('english'::regconfig, (((COALESCE(name, ''::text) || ' '::text) || (COALESCE(description, ''::text) || ' '::text)) || COALESCE(location, ''::text)))`),
}, (table) => [
	index("construction_projects_fts").using("gin", table.fts.asc().nullsLast().op("tsvector_ops")),
	index("construction_projects_status_idx").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	index("construction_projects_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "construction_projects_created_by_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.customerId],
			foreignColumns: [customers.id],
			name: "construction_projects_customer_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "construction_projects_team_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Construction projects can be created by a member of the team", { as: "permissive", for: "insert", to: ["authenticated"], withCheck: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))`  }),
	pgPolicy("Construction projects can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["authenticated"] }),
	pgPolicy("Construction projects can be selected by a member of the team", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Construction projects can be updated by a member of the team", { as: "permissive", for: "update", to: ["authenticated"] }),
]);

export const constructionAnnotations = pgTable("construction_annotations", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	projectId: uuid("project_id").notNull(),
	teamId: uuid("team_id").notNull(),
	userId: uuid("user_id").notNull(),
	type: text().notNull(),
	content: text().notNull(),
	position: jsonb().notNull(),
	attachments: text().array(),
	status: text().default('active'),
	relatedPhase: constructionPhase(),
	priority: text().default('normal'),
	assignedTo: uuid("assigned_to"),
	resolvedAt: timestamp("resolved_at", { withTimezone: true, mode: 'string' }),
	resolvedBy: uuid("resolved_by"),
}, (table) => [
	index("construction_annotations_project_id_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("construction_annotations_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.assignedTo],
			foreignColumns: [users.id],
			name: "construction_annotations_assigned_to_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [constructionProjects.id],
			name: "construction_annotations_project_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.resolvedBy],
			foreignColumns: [users.id],
			name: "construction_annotations_resolved_by_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "construction_annotations_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "construction_annotations_user_id_fkey"
		}).onDelete("cascade"),
	pgPolicy("Annotations can be created by a member of the team", { as: "permissive", for: "insert", to: ["authenticated"], withCheck: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))`  }),
	pgPolicy("Annotations can be selected by a member of the team", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Annotations can be updated by creator or assignee", { as: "permissive", for: "update", to: ["authenticated"] }),
]);

export const tags = pgTable("tags", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	teamId: uuid("team_id").notNull(),
	name: text().notNull(),
}, (table) => [
	index("tags_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "tags_team_id_fkey"
		}).onDelete("cascade"),
	unique("unique_tag_name").on(table.teamId, table.name),
	pgPolicy("Tags can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const inbox = pgTable("inbox", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	teamId: uuid("team_id"),
	filePath: text("file_path").array(),
	fileName: text("file_name"),
	transactionId: uuid("transaction_id"),
	amount: numeric({ precision: 10, scale:  2 }),
	currency: text(),
	contentType: text("content_type"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	size: bigint({ mode: "number" }),
	attachmentId: uuid("attachment_id"),
	date: date(),
	forwardedTo: text("forwarded_to"),
	referenceId: text("reference_id"),
	meta: json(),
	status: inboxStatus().default('new'),
	website: text(),
	displayName: text("display_name"),
	// TODO: failed to parse database type 'tsvector'
	fts: unknown("fts").notNull().generatedAlwaysAs(sql`generate_inbox_fts(display_name, extract_product_names((meta -> 'products'::text)))`),
	type: inboxType(),
	description: text(),
	baseAmount: numeric("base_amount", { precision: 10, scale:  2 }),
	baseCurrency: text("base_currency"),
	taxAmount: numeric("tax_amount", { precision: 10, scale:  2 }),
	taxRate: numeric("tax_rate", { precision: 10, scale:  2 }),
	taxType: text("tax_type"),
}, (table) => [
	index("inbox_attachment_id_idx").using("btree", table.attachmentId.asc().nullsLast().op("uuid_ops")),
	index("inbox_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("inbox_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("inbox_transaction_id_idx").using("btree", table.transactionId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.attachmentId],
			foreignColumns: [transactionAttachments.id],
			name: "inbox_attachment_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "public_inbox_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.transactionId],
			foreignColumns: [transactions.id],
			name: "public_inbox_transaction_id_fkey"
		}).onDelete("set null"),
	unique("inbox_reference_id_key").on(table.referenceId),
	pgPolicy("Inbox can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
	pgPolicy("Inbox can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("Inbox can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
]);

export const userInvites = pgTable("user_invites", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	teamId: uuid("team_id"),
	email: text(),
	role: teamRoles(),
	code: text().default('nanoid(24)'),
	invitedBy: uuid("invited_by"),
}, (table) => [
	index("user_invites_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "public_user_invites_team_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.invitedBy],
			foreignColumns: [users.id],
			name: "user_invites_invited_by_fkey"
		}).onDelete("cascade"),
	unique("unique_team_invite").on(table.teamId, table.email),
	unique("user_invites_code_key").on(table.code),
	pgPolicy("Enable select for users based on email", { as: "permissive", for: "select", to: ["public"], using: sql`((auth.jwt() ->> 'email'::text) = email)` }),
	pgPolicy("User Invites can be created by a member of the team", { as: "permissive", for: "insert", to: ["public"] }),
	pgPolicy("User Invites can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("User Invites can be deleted by invited email", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("User Invites can be selected by a member of the team", { as: "permissive", for: "select", to: ["public"] }),
	pgPolicy("User Invites can be updated by a member of the team", { as: "permissive", for: "update", to: ["public"] }),
]);

export const documentTagAssignments = pgTable("document_tag_assignments", {
	documentId: uuid("document_id").notNull(),
	tagId: uuid("tag_id").notNull(),
	teamId: uuid("team_id").notNull(),
}, (table) => [
	index("idx_document_tag_assignments_document_id").using("btree", table.documentId.asc().nullsLast().op("uuid_ops")),
	index("idx_document_tag_assignments_tag_id").using("btree", table.tagId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.documentId],
			foreignColumns: [documents.id],
			name: "document_tag_assignments_document_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.tagId],
			foreignColumns: [documentTags.id],
			name: "document_tag_assignments_tag_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "document_tag_assignments_team_id_fkey"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.documentId, table.tagId], name: "document_tag_assignments_pkey"}),
	pgPolicy("Tags can be handled by a member of the team", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);

export const usersOnTeam = pgTable("users_on_team", {
	userId: uuid("user_id").notNull(),
	teamId: uuid("team_id").notNull(),
	id: uuid().defaultRandom().notNull(),
	role: teamRoles(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_users_on_team_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	index("users_on_team_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	index("users_on_team_user_id_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "users_on_team_team_id_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "users_on_team_user_id_fkey"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.userId, table.teamId, table.id], name: "members_pkey"}),
	pgPolicy("Enable insert for authenticated users only", { as: "permissive", for: "insert", to: ["authenticated"], withCheck: sql`true`  }),
	pgPolicy("Enable updates for users on team", { as: "permissive", for: "update", to: ["authenticated"] }),
	pgPolicy("Select for current user teams", { as: "permissive", for: "select", to: ["authenticated"] }),
	pgPolicy("Users on team can be deleted by a member of the team", { as: "permissive", for: "delete", to: ["public"] }),
	pgPolicy("Users on team can be selected", { as: "permissive", for: "select", to: ["authenticated"] }),
]);

export const transactionCategories = pgTable("transaction_categories", {
	id: uuid().defaultRandom().notNull(),
	name: text().notNull(),
	teamId: uuid("team_id").notNull(),
	color: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	system: boolean().default(false),
	slug: text().notNull(),
	vat: numeric({ precision: 10, scale:  2 }),
	description: text(),
	embedding: vector({ dimensions: 384 }),
}, (table) => [
	index("transaction_categories_team_id_idx").using("btree", table.teamId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "transaction_categories_team_id_fkey"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.teamId, table.slug], name: "transaction_categories_pkey"}),
	pgPolicy("Users on team can manage categories", { as: "permissive", for: "all", to: ["public"], using: sql`(team_id IN ( SELECT private.get_teams_for_authenticated_user() AS get_teams_for_authenticated_user))` }),
]);
export const projectDashboardView = pgView("project_dashboard_view", {	id: uuid(),
	name: text(),
	status: trackerStatus(),
	projectType: text("project_type"),
	priority: projectPriority(),
	completionPercentage: numeric("completion_percentage", { precision: 5, scale:  2 }),
	budget: numeric({ precision: 15, scale:  2 }),
	actualCost: numeric("actual_cost", { precision: 15, scale:  2 }),
	plannedStartDate: date("planned_start_date"),
	plannedEndDate: date("planned_end_date"),
	actualStartDate: date("actual_start_date"),
	actualEndDate: date("actual_end_date"),
	customerName: text("customer_name"),
	projectManagerName: text("project_manager_name"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	totalTasks: bigint("total_tasks", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	completedTasks: bigint("completed_tasks", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	teamSize: bigint("team_size", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	openSafetyIncidents: bigint("open_safety_incidents", { mode: "number" }),
}).as(sql`SELECT tp.id, tp.name, tp.status, tp.project_type, tp.priority, tp.completion_percentage, tp.budget, tp.actual_cost, tp.planned_start_date, tp.planned_end_date, tp.actual_start_date, tp.actual_end_date, c.name AS customer_name, u.full_name AS project_manager_name, ( SELECT count(*) AS count FROM project_tasks pt WHERE pt.project_id = tp.id) AS total_tasks, ( SELECT count(*) AS count FROM project_tasks pt WHERE pt.project_id = tp.id AND pt.status = 'completed'::task_status) AS completed_tasks, ( SELECT count(*) AS count FROM project_team_members ptm WHERE ptm.project_id = tp.id AND ptm.is_active = true) AS team_size, ( SELECT count(*) AS count FROM safety_incidents si WHERE si.project_id = tp.id AND si.status = 'open'::text) AS open_safety_incidents FROM tracker_projects tp LEFT JOIN customers c ON tp.customer_id = c.id LEFT JOIN users u ON tp.project_manager_id = u.id`);

export const teamLimitsMetrics = pgMaterializedView("team_limits_metrics", {	teamId: uuid("team_id"),
	totalDocumentSize: numeric("total_document_size"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	numberOfUsers: bigint("number_of_users", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	numberOfBankConnections: bigint("number_of_bank_connections", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	invoicesCreatedThisMonth: bigint("invoices_created_this_month", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	inboxCreatedThisMonth: bigint("inbox_created_this_month", { mode: "number" }),
}).as(sql`SELECT t.id AS team_id, COALESCE(sum((d.metadata ->> 'size'::text)::bigint), 0::numeric) AS total_document_size, count(DISTINCT u.id) AS number_of_users, count(DISTINCT bc.id) AS number_of_bank_connections, count(DISTINCT i.id) FILTER (WHERE date_trunc('month'::text, i.created_at) = date_trunc('month'::text, CURRENT_DATE::timestamp with time zone)) AS invoices_created_this_month, count(DISTINCT inbox.id) FILTER (WHERE date_trunc('month'::text, inbox.created_at) = date_trunc('month'::text, CURRENT_DATE::timestamp with time zone)) AS inbox_created_this_month FROM teams t LEFT JOIN documents d ON d.team_id = t.id LEFT JOIN users u ON u.team_id = t.id LEFT JOIN bank_connections bc ON bc.team_id = t.id LEFT JOIN invoices i ON i.team_id = t.id LEFT JOIN inbox ON inbox.team_id = t.id GROUP BY t.id`);