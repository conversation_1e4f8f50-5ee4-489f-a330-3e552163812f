import { relations } from "drizzle-orm/relations";
import { users, projectPermissions, trackerProjects, trackerEntries, teams, resources, projectTypes, projects, invoices, customers, materials, projectMembers, roles, tasks, projectFiles, customerTags, tags, transactions, bankAccounts, transactionCategories, inboxAccounts, resourceAllocations, documentTags, trackerReports, bankConnections, reports, transactionEnrichments, invoiceTemplates, projectTrades, apps, documents, projectModels, modelAnnotations, annotationThreads, transactionAttachments, trackerProjectTags, transactionTags, apiKeys, projectTasks, projectPhases, bimModels, projectTeamMembers, projectResources, projectTemplates, projectCommunications, projectInspections, safetyIncidents, projectProgressLogs, changeOrders, constructionProjects, constructionProgressUpdates, constructionAnnotations, inbox, userInvites, documentTagAssignments, usersOnTeam } from "./schema";

export const projectPermissionsRelations = relations(projectPermissions, ({one}) => ({
	user_grantedBy: one(users, {
		fields: [projectPermissions.grantedBy],
		references: [users.id],
		relationName: "projectPermissions_grantedBy_users_id"
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectPermissions.projectId],
		references: [trackerProjects.id]
	}),
	user_userId: one(users, {
		fields: [projectPermissions.userId],
		references: [users.id],
		relationName: "projectPermissions_userId_users_id"
	}),
}));

export const usersRelations = relations(users, ({one, many}) => ({
	projectPermissions_grantedBy: many(projectPermissions, {
		relationName: "projectPermissions_grantedBy_users_id"
	}),
	projectPermissions_userId: many(projectPermissions, {
		relationName: "projectPermissions_userId_users_id"
	}),
	trackerEntries: many(trackerEntries),
	invoices: many(invoices),
	projectMembers: many(projectMembers),
	projectFiles: many(projectFiles),
	transactions: many(transactions),
	trackerReports: many(trackerReports),
	reports: many(reports),
	user: one(users, {
		fields: [users.id],
		references: [users.id],
		relationName: "users_id_users_id"
	}),
	users: many(users, {
		relationName: "users_id_users_id"
	}),
	team: one(teams, {
		fields: [users.teamId],
		references: [teams.id]
	}),
	projectTrades: many(projectTrades),
	bankAccounts: many(bankAccounts),
	apps: many(apps),
	documents: many(documents),
	projectModels: many(projectModels),
	modelAnnotations_assignedTo: many(modelAnnotations, {
		relationName: "modelAnnotations_assignedTo_users_id"
	}),
	modelAnnotations_resolvedBy: many(modelAnnotations, {
		relationName: "modelAnnotations_resolvedBy_users_id"
	}),
	modelAnnotations_userId: many(modelAnnotations, {
		relationName: "modelAnnotations_userId_users_id"
	}),
	annotationThreads: many(annotationThreads),
	apiKeys: many(apiKeys),
	projectTasks_assignedTo: many(projectTasks, {
		relationName: "projectTasks_assignedTo_users_id"
	}),
	projectTasks_createdBy: many(projectTasks, {
		relationName: "projectTasks_createdBy_users_id"
	}),
	projectTeamMembers: many(projectTeamMembers),
	projectTemplates: many(projectTemplates),
	trackerProjects: many(trackerProjects),
	projectCommunications: many(projectCommunications),
	projectInspections: many(projectInspections),
	safetyIncidents: many(safetyIncidents),
	projectProgressLogs: many(projectProgressLogs),
	changeOrders_approvedBy: many(changeOrders, {
		relationName: "changeOrders_approvedBy_users_id"
	}),
	changeOrders_requestedBy: many(changeOrders, {
		relationName: "changeOrders_requestedBy_users_id"
	}),
	constructionProgressUpdates: many(constructionProgressUpdates),
	constructionProjects: many(constructionProjects),
	constructionAnnotations_assignedTo: many(constructionAnnotations, {
		relationName: "constructionAnnotations_assignedTo_users_id"
	}),
	constructionAnnotations_resolvedBy: many(constructionAnnotations, {
		relationName: "constructionAnnotations_resolvedBy_users_id"
	}),
	constructionAnnotations_userId: many(constructionAnnotations, {
		relationName: "constructionAnnotations_userId_users_id"
	}),
	userInvites: many(userInvites),
	usersOnTeams: many(usersOnTeam),
}));

export const trackerProjectsRelations = relations(trackerProjects, ({one, many}) => ({
	projectPermissions: many(projectPermissions),
	trackerEntries: many(trackerEntries),
	materials: many(materials),
	resourceAllocations: many(resourceAllocations),
	trackerReports: many(trackerReports),
	projectTrades: many(projectTrades),
	projectModels: many(projectModels),
	trackerProjectTags: many(trackerProjectTags),
	projectTasks: many(projectTasks),
	bimModels: many(bimModels),
	projectTeamMembers: many(projectTeamMembers),
	projectResources: many(projectResources),
	projectPhases: many(projectPhases),
	customer: one(customers, {
		fields: [trackerProjects.customerId],
		references: [customers.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerProjects.parentProjectId],
		references: [trackerProjects.id],
		relationName: "trackerProjects_parentProjectId_trackerProjects_id"
	}),
	trackerProjects: many(trackerProjects, {
		relationName: "trackerProjects_parentProjectId_trackerProjects_id"
	}),
	user: one(users, {
		fields: [trackerProjects.projectManagerId],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [trackerProjects.teamId],
		references: [teams.id]
	}),
	projectTemplate: one(projectTemplates, {
		fields: [trackerProjects.templateId],
		references: [projectTemplates.id]
	}),
	projectCommunications: many(projectCommunications),
	projectInspections: many(projectInspections),
	safetyIncidents: many(safetyIncidents),
	projectProgressLogs: many(projectProgressLogs),
	changeOrders: many(changeOrders),
}));

export const trackerEntriesRelations = relations(trackerEntries, ({one}) => ({
	user: one(users, {
		fields: [trackerEntries.assignedId],
		references: [users.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerEntries.projectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [trackerEntries.teamId],
		references: [teams.id]
	}),
}));

export const teamsRelations = relations(teams, ({many}) => ({
	trackerEntries: many(trackerEntries),
	resources: many(resources),
	invoices: many(invoices),
	materials: many(materials),
	customerTags: many(customerTags),
	transactions: many(transactions),
	inboxAccounts: many(inboxAccounts),
	resourceAllocations: many(resourceAllocations),
	customers: many(customers),
	documentTags: many(documentTags),
	trackerReports: many(trackerReports),
	bankConnections: many(bankConnections),
	reports: many(reports),
	users: many(users),
	transactionEnrichments: many(transactionEnrichments),
	invoiceTemplates: many(invoiceTemplates),
	projectTrades: many(projectTrades),
	bankAccounts: many(bankAccounts),
	apps: many(apps),
	documents: many(documents),
	transactionAttachments: many(transactionAttachments),
	trackerProjectTags: many(trackerProjectTags),
	transactionTags: many(transactionTags),
	apiKeys: many(apiKeys),
	bimModels: many(bimModels),
	projectTemplates: many(projectTemplates),
	trackerProjects: many(trackerProjects),
	constructionProgressUpdates: many(constructionProgressUpdates),
	constructionProjects: many(constructionProjects),
	constructionAnnotations: many(constructionAnnotations),
	tags: many(tags),
	inboxes: many(inbox),
	userInvites: many(userInvites),
	documentTagAssignments: many(documentTagAssignments),
	usersOnTeams: many(usersOnTeam),
	transactionCategories: many(transactionCategories),
}));

export const resourcesRelations = relations(resources, ({one, many}) => ({
	team: one(teams, {
		fields: [resources.teamId],
		references: [teams.id]
	}),
	resourceAllocations: many(resourceAllocations),
}));

export const projectsRelations = relations(projects, ({one, many}) => ({
	projectType: one(projectTypes, {
		fields: [projects.projectTypeId],
		references: [projectTypes.id]
	}),
	invoices: many(invoices),
	projectMembers: many(projectMembers),
	tasks: many(tasks),
	projectFiles: many(projectFiles),
}));

export const projectTypesRelations = relations(projectTypes, ({many}) => ({
	projects: many(projects),
}));

export const invoicesRelations = relations(invoices, ({one}) => ({
	user: one(users, {
		fields: [invoices.userId],
		references: [users.id]
	}),
	customer: one(customers, {
		fields: [invoices.customerId],
		references: [customers.id]
	}),
	project: one(projects, {
		fields: [invoices.projectId],
		references: [projects.id]
	}),
	team: one(teams, {
		fields: [invoices.teamId],
		references: [teams.id]
	}),
}));

export const customersRelations = relations(customers, ({one, many}) => ({
	invoices: many(invoices),
	customerTags: many(customerTags),
	team: one(teams, {
		fields: [customers.teamId],
		references: [teams.id]
	}),
	trackerProjects: many(trackerProjects),
	constructionProjects: many(constructionProjects),
}));

export const materialsRelations = relations(materials, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [materials.projectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [materials.teamId],
		references: [teams.id]
	}),
}));

export const projectMembersRelations = relations(projectMembers, ({one, many}) => ({
	project: one(projects, {
		fields: [projectMembers.projectId],
		references: [projects.id]
	}),
	role: one(roles, {
		fields: [projectMembers.roleId],
		references: [roles.id]
	}),
	user: one(users, {
		fields: [projectMembers.userId],
		references: [users.id]
	}),
	tasks: many(tasks),
}));

export const rolesRelations = relations(roles, ({many}) => ({
	projectMembers: many(projectMembers),
}));

export const tasksRelations = relations(tasks, ({one, many}) => ({
	projectMember: one(projectMembers, {
		fields: [tasks.assignedToMemberId],
		references: [projectMembers.id]
	}),
	task: one(tasks, {
		fields: [tasks.parentTaskId],
		references: [tasks.id],
		relationName: "tasks_parentTaskId_tasks_id"
	}),
	tasks: many(tasks, {
		relationName: "tasks_parentTaskId_tasks_id"
	}),
	project: one(projects, {
		fields: [tasks.projectId],
		references: [projects.id]
	}),
}));

export const projectFilesRelations = relations(projectFiles, ({one}) => ({
	project: one(projects, {
		fields: [projectFiles.projectId],
		references: [projects.id]
	}),
	user: one(users, {
		fields: [projectFiles.userId],
		references: [users.id]
	}),
}));

export const customerTagsRelations = relations(customerTags, ({one}) => ({
	customer: one(customers, {
		fields: [customerTags.customerId],
		references: [customers.id]
	}),
	tag: one(tags, {
		fields: [customerTags.tagId],
		references: [tags.id]
	}),
	team: one(teams, {
		fields: [customerTags.teamId],
		references: [teams.id]
	}),
}));

export const tagsRelations = relations(tags, ({one, many}) => ({
	customerTags: many(customerTags),
	trackerProjectTags: many(trackerProjectTags),
	transactionTags: many(transactionTags),
	team: one(teams, {
		fields: [tags.teamId],
		references: [teams.id]
	}),
}));

export const transactionsRelations = relations(transactions, ({one, many}) => ({
	user: one(users, {
		fields: [transactions.assignedId],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [transactions.teamId],
		references: [teams.id]
	}),
	bankAccount: one(bankAccounts, {
		fields: [transactions.bankAccountId],
		references: [bankAccounts.id]
	}),
	transactionCategory: one(transactionCategories, {
		fields: [transactions.teamId],
		references: [transactionCategories.teamId]
	}),
	transactionAttachments: many(transactionAttachments),
	transactionTags: many(transactionTags),
	inboxes: many(inbox),
}));

export const bankAccountsRelations = relations(bankAccounts, ({one, many}) => ({
	transactions: many(transactions),
	bankConnection: one(bankConnections, {
		fields: [bankAccounts.bankConnectionId],
		references: [bankConnections.id]
	}),
	user: one(users, {
		fields: [bankAccounts.createdBy],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [bankAccounts.teamId],
		references: [teams.id]
	}),
}));

export const transactionCategoriesRelations = relations(transactionCategories, ({one, many}) => ({
	transactions: many(transactions),
	transactionEnrichments: many(transactionEnrichments),
	team: one(teams, {
		fields: [transactionCategories.teamId],
		references: [teams.id]
	}),
}));

export const inboxAccountsRelations = relations(inboxAccounts, ({one}) => ({
	team: one(teams, {
		fields: [inboxAccounts.teamId],
		references: [teams.id]
	}),
}));

export const resourceAllocationsRelations = relations(resourceAllocations, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [resourceAllocations.projectId],
		references: [trackerProjects.id]
	}),
	resource: one(resources, {
		fields: [resourceAllocations.resourceId],
		references: [resources.id]
	}),
	team: one(teams, {
		fields: [resourceAllocations.teamId],
		references: [teams.id]
	}),
}));

export const documentTagsRelations = relations(documentTags, ({one, many}) => ({
	team: one(teams, {
		fields: [documentTags.teamId],
		references: [teams.id]
	}),
	documentTagAssignments: many(documentTagAssignments),
}));

export const trackerReportsRelations = relations(trackerReports, ({one}) => ({
	user: one(users, {
		fields: [trackerReports.createdBy],
		references: [users.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerReports.projectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [trackerReports.teamId],
		references: [teams.id]
	}),
}));

export const bankConnectionsRelations = relations(bankConnections, ({one, many}) => ({
	team: one(teams, {
		fields: [bankConnections.teamId],
		references: [teams.id]
	}),
	bankAccounts: many(bankAccounts),
}));

export const reportsRelations = relations(reports, ({one}) => ({
	user: one(users, {
		fields: [reports.createdBy],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [reports.teamId],
		references: [teams.id]
	}),
}));

export const transactionEnrichmentsRelations = relations(transactionEnrichments, ({one}) => ({
	transactionCategory: one(transactionCategories, {
		fields: [transactionEnrichments.teamId],
		references: [transactionCategories.teamId]
	}),
	team: one(teams, {
		fields: [transactionEnrichments.teamId],
		references: [teams.id]
	}),
}));

export const invoiceTemplatesRelations = relations(invoiceTemplates, ({one}) => ({
	team: one(teams, {
		fields: [invoiceTemplates.teamId],
		references: [teams.id]
	}),
}));

export const projectTradesRelations = relations(projectTrades, ({one}) => ({
	team: one(teams, {
		fields: [projectTrades.assignedTeamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [projectTrades.foremanId],
		references: [users.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectTrades.projectId],
		references: [trackerProjects.id]
	}),
}));

export const appsRelations = relations(apps, ({one}) => ({
	user: one(users, {
		fields: [apps.createdBy],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [apps.teamId],
		references: [teams.id]
	}),
}));

export const documentsRelations = relations(documents, ({one, many}) => ({
	user: one(users, {
		fields: [documents.ownerId],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [documents.teamId],
		references: [teams.id]
	}),
	bimModels: many(bimModels),
	documentTagAssignments: many(documentTagAssignments),
}));

export const projectModelsRelations = relations(projectModels, ({one, many}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectModels.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [projectModels.uploadedBy],
		references: [users.id]
	}),
	modelAnnotations: many(modelAnnotations),
}));

export const modelAnnotationsRelations = relations(modelAnnotations, ({one, many}) => ({
	user_assignedTo: one(users, {
		fields: [modelAnnotations.assignedTo],
		references: [users.id],
		relationName: "modelAnnotations_assignedTo_users_id"
	}),
	projectModel: one(projectModels, {
		fields: [modelAnnotations.modelId],
		references: [projectModels.id]
	}),
	user_resolvedBy: one(users, {
		fields: [modelAnnotations.resolvedBy],
		references: [users.id],
		relationName: "modelAnnotations_resolvedBy_users_id"
	}),
	user_userId: one(users, {
		fields: [modelAnnotations.userId],
		references: [users.id],
		relationName: "modelAnnotations_userId_users_id"
	}),
	annotationThreads: many(annotationThreads),
}));

export const annotationThreadsRelations = relations(annotationThreads, ({one}) => ({
	modelAnnotation: one(modelAnnotations, {
		fields: [annotationThreads.annotationId],
		references: [modelAnnotations.id]
	}),
	user: one(users, {
		fields: [annotationThreads.userId],
		references: [users.id]
	}),
}));

export const transactionAttachmentsRelations = relations(transactionAttachments, ({one, many}) => ({
	team: one(teams, {
		fields: [transactionAttachments.teamId],
		references: [teams.id]
	}),
	transaction: one(transactions, {
		fields: [transactionAttachments.transactionId],
		references: [transactions.id]
	}),
	inboxes: many(inbox),
}));

export const trackerProjectTagsRelations = relations(trackerProjectTags, ({one}) => ({
	tag: one(tags, {
		fields: [trackerProjectTags.tagId],
		references: [tags.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [trackerProjectTags.trackerProjectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [trackerProjectTags.teamId],
		references: [teams.id]
	}),
}));

export const transactionTagsRelations = relations(transactionTags, ({one}) => ({
	tag: one(tags, {
		fields: [transactionTags.tagId],
		references: [tags.id]
	}),
	team: one(teams, {
		fields: [transactionTags.teamId],
		references: [teams.id]
	}),
	transaction: one(transactions, {
		fields: [transactionTags.transactionId],
		references: [transactions.id]
	}),
}));

export const apiKeysRelations = relations(apiKeys, ({one}) => ({
	team: one(teams, {
		fields: [apiKeys.teamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [apiKeys.userId],
		references: [users.id]
	}),
}));

export const projectTasksRelations = relations(projectTasks, ({one, many}) => ({
	user_assignedTo: one(users, {
		fields: [projectTasks.assignedTo],
		references: [users.id],
		relationName: "projectTasks_assignedTo_users_id"
	}),
	user_createdBy: one(users, {
		fields: [projectTasks.createdBy],
		references: [users.id],
		relationName: "projectTasks_createdBy_users_id"
	}),
	projectTask: one(projectTasks, {
		fields: [projectTasks.parentTaskId],
		references: [projectTasks.id],
		relationName: "projectTasks_parentTaskId_projectTasks_id"
	}),
	projectTasks: many(projectTasks, {
		relationName: "projectTasks_parentTaskId_projectTasks_id"
	}),
	projectPhase: one(projectPhases, {
		fields: [projectTasks.phaseId],
		references: [projectPhases.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectTasks.projectId],
		references: [trackerProjects.id]
	}),
	projectResources: many(projectResources),
	projectInspections: many(projectInspections),
	projectProgressLogs: many(projectProgressLogs),
}));

export const projectPhasesRelations = relations(projectPhases, ({one, many}) => ({
	projectTasks: many(projectTasks),
	trackerProject: one(trackerProjects, {
		fields: [projectPhases.projectId],
		references: [trackerProjects.id]
	}),
	projectInspections: many(projectInspections),
	projectProgressLogs: many(projectProgressLogs),
}));

export const bimModelsRelations = relations(bimModels, ({one}) => ({
	document: one(documents, {
		fields: [bimModels.documentId],
		references: [documents.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [bimModels.projectId],
		references: [trackerProjects.id]
	}),
	team: one(teams, {
		fields: [bimModels.teamId],
		references: [teams.id]
	}),
}));

export const projectTeamMembersRelations = relations(projectTeamMembers, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectTeamMembers.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [projectTeamMembers.userId],
		references: [users.id]
	}),
}));

export const projectResourcesRelations = relations(projectResources, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectResources.projectId],
		references: [trackerProjects.id]
	}),
	projectTask: one(projectTasks, {
		fields: [projectResources.taskId],
		references: [projectTasks.id]
	}),
}));

export const projectTemplatesRelations = relations(projectTemplates, ({one, many}) => ({
	user: one(users, {
		fields: [projectTemplates.createdBy],
		references: [users.id]
	}),
	team: one(teams, {
		fields: [projectTemplates.teamId],
		references: [teams.id]
	}),
	trackerProjects: many(trackerProjects),
}));

export const projectCommunicationsRelations = relations(projectCommunications, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [projectCommunications.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [projectCommunications.senderId],
		references: [users.id]
	}),
}));

export const projectInspectionsRelations = relations(projectInspections, ({one}) => ({
	user: one(users, {
		fields: [projectInspections.inspectorId],
		references: [users.id]
	}),
	projectPhase: one(projectPhases, {
		fields: [projectInspections.phaseId],
		references: [projectPhases.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectInspections.projectId],
		references: [trackerProjects.id]
	}),
	projectTask: one(projectTasks, {
		fields: [projectInspections.taskId],
		references: [projectTasks.id]
	}),
}));

export const safetyIncidentsRelations = relations(safetyIncidents, ({one}) => ({
	trackerProject: one(trackerProjects, {
		fields: [safetyIncidents.projectId],
		references: [trackerProjects.id]
	}),
	user: one(users, {
		fields: [safetyIncidents.reportedBy],
		references: [users.id]
	}),
}));

export const projectProgressLogsRelations = relations(projectProgressLogs, ({one}) => ({
	user: one(users, {
		fields: [projectProgressLogs.loggedBy],
		references: [users.id]
	}),
	projectPhase: one(projectPhases, {
		fields: [projectProgressLogs.phaseId],
		references: [projectPhases.id]
	}),
	trackerProject: one(trackerProjects, {
		fields: [projectProgressLogs.projectId],
		references: [trackerProjects.id]
	}),
	projectTask: one(projectTasks, {
		fields: [projectProgressLogs.taskId],
		references: [projectTasks.id]
	}),
}));

export const changeOrdersRelations = relations(changeOrders, ({one}) => ({
	user_approvedBy: one(users, {
		fields: [changeOrders.approvedBy],
		references: [users.id],
		relationName: "changeOrders_approvedBy_users_id"
	}),
	trackerProject: one(trackerProjects, {
		fields: [changeOrders.projectId],
		references: [trackerProjects.id]
	}),
	user_requestedBy: one(users, {
		fields: [changeOrders.requestedBy],
		references: [users.id],
		relationName: "changeOrders_requestedBy_users_id"
	}),
}));

export const constructionProgressUpdatesRelations = relations(constructionProgressUpdates, ({one}) => ({
	constructionProject: one(constructionProjects, {
		fields: [constructionProgressUpdates.projectId],
		references: [constructionProjects.id]
	}),
	team: one(teams, {
		fields: [constructionProgressUpdates.teamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [constructionProgressUpdates.userId],
		references: [users.id]
	}),
}));

export const constructionProjectsRelations = relations(constructionProjects, ({one, many}) => ({
	constructionProgressUpdates: many(constructionProgressUpdates),
	user: one(users, {
		fields: [constructionProjects.createdBy],
		references: [users.id]
	}),
	customer: one(customers, {
		fields: [constructionProjects.customerId],
		references: [customers.id]
	}),
	team: one(teams, {
		fields: [constructionProjects.teamId],
		references: [teams.id]
	}),
	constructionAnnotations: many(constructionAnnotations),
}));

export const constructionAnnotationsRelations = relations(constructionAnnotations, ({one}) => ({
	user_assignedTo: one(users, {
		fields: [constructionAnnotations.assignedTo],
		references: [users.id],
		relationName: "constructionAnnotations_assignedTo_users_id"
	}),
	constructionProject: one(constructionProjects, {
		fields: [constructionAnnotations.projectId],
		references: [constructionProjects.id]
	}),
	user_resolvedBy: one(users, {
		fields: [constructionAnnotations.resolvedBy],
		references: [users.id],
		relationName: "constructionAnnotations_resolvedBy_users_id"
	}),
	team: one(teams, {
		fields: [constructionAnnotations.teamId],
		references: [teams.id]
	}),
	user_userId: one(users, {
		fields: [constructionAnnotations.userId],
		references: [users.id],
		relationName: "constructionAnnotations_userId_users_id"
	}),
}));

export const inboxRelations = relations(inbox, ({one}) => ({
	transactionAttachment: one(transactionAttachments, {
		fields: [inbox.attachmentId],
		references: [transactionAttachments.id]
	}),
	team: one(teams, {
		fields: [inbox.teamId],
		references: [teams.id]
	}),
	transaction: one(transactions, {
		fields: [inbox.transactionId],
		references: [transactions.id]
	}),
}));

export const userInvitesRelations = relations(userInvites, ({one}) => ({
	team: one(teams, {
		fields: [userInvites.teamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [userInvites.invitedBy],
		references: [users.id]
	}),
}));

export const documentTagAssignmentsRelations = relations(documentTagAssignments, ({one}) => ({
	document: one(documents, {
		fields: [documentTagAssignments.documentId],
		references: [documents.id]
	}),
	documentTag: one(documentTags, {
		fields: [documentTagAssignments.tagId],
		references: [documentTags.id]
	}),
	team: one(teams, {
		fields: [documentTagAssignments.teamId],
		references: [teams.id]
	}),
}));

export const usersOnTeamRelations = relations(usersOnTeam, ({one}) => ({
	team: one(teams, {
		fields: [usersOnTeam.teamId],
		references: [teams.id]
	}),
	user: one(users, {
		fields: [usersOnTeam.userId],
		references: [users.id]
	}),
}));