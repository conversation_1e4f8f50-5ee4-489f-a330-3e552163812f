-- Function to get team bank accounts balances
CREATE OR REPLACE FUNCTION get_team_bank_accounts_balances(p_team_id UUID)
RETURNS TABLE (
  id UUID,
  currency TEXT,
  balance NUMERIC(10, 2),
  name TEXT,
  logo_url TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ba.id,
    ba.currency,
    ba.balance,
    ba.name,
    bc.logo_url
  FROM bank_accounts ba
  LEFT JOIN bank_connections bc ON ba.bank_connection_id = bc.id
  WHERE ba.team_id = p_team_id
    AND ba.enabled = true
  ORDER BY ba.created_at ASC, ba.name DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get bank account currencies
CREATE OR REPLACE FUNCTION get_bank_account_currencies(p_team_id UUID)
RETURNS TABLE (
  currency TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ba.currency
  FROM bank_accounts ba
  WHERE ba.team_id = p_team_id
    AND ba.currency IS NOT NULL
    AND ba.enabled = true
  ORDER BY ba.currency;
END;
$$ LANGUAGE plpgsql;

-- Function to generate inbox FTS
CREATE OR REPLACE FUNCTION generate_inbox_fts(display_name TEXT, product_names TEXT)
RETURNS tsvector AS $$
BEGIN
  RETURN to_tsvector(
    'english',
    COALESCE(display_name, '') || ' ' || COALESCE(product_names, '')
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to extract product names
CREATE OR REPLACE FUNCTION extract_product_names(products_json JSON)
RETURNS TEXT AS $$
DECLARE
  product_names TEXT := '';
  product JSON;
BEGIN
  IF products_json IS NULL THEN
    RETURN '';
  END IF;
  
  FOR product IN SELECT * FROM json_array_elements(products_json)
  LOOP
    product_names := product_names || ' ' || COALESCE(product->>'name', '');
  END LOOP;
  
  RETURN TRIM(product_names);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create generate_inbox function if it doesn't exist
CREATE OR REPLACE FUNCTION generate_inbox(length INTEGER)
RETURNS TEXT AS $$
DECLARE
  chars TEXT := 'abcdefghijklmnopqrstuvwxyz0123456789';
  result TEXT := '';
  i INTEGER;
BEGIN
  FOR i IN 1..length LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::INTEGER, 1);
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create nanoid function if it doesn't exist
CREATE OR REPLACE FUNCTION nanoid(
  size int DEFAULT 21,
  alphabet text DEFAULT '_-0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
)
RETURNS text
LANGUAGE plpgsql
VOLATILE
AS $$
DECLARE
  id text := '';
  i int := 0;
  urlAlphabet char(64) := alphabet;
  bytes bytea := gen_random_bytes(size);
  byte int;
  pos int;
BEGIN
  WHILE i < size LOOP
    byte := get_byte(bytes, i);
    pos := (byte & 63) + 1;
    id := id || substr(urlAlphabet, pos, 1);
    i = i + 1;
  END LOOP;
  RETURN id;
END
$$;