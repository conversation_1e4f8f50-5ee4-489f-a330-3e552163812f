import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import { config } from "dotenv";
import { eq } from "drizzle-orm";
import {
  teams,
  users,
  usersOnTeam,
  bankAccounts,
  invoiceTemplates,
  transactions,
  transactionCategories,
} from "./src/db/schema";
import * as schema from "./src/db/schema";
import { nanoid } from "nanoid";
import { sql } from "drizzle-orm";

// Load environment variables
config({ path: ".env.local" });

const pool = new Pool({
  connectionString: process.env.DATABASE_SESSION_POOLER,
});

const db = drizzle(pool, { schema, casing: "snake_case" });

async function seed() {
  console.log("Starting seed...");

  try {
    // Use the existing team ID
    const teamId = "a8cd4d5d-95c0-4f3c-9f4e-bf945b775813";
    console.log(`Using existing team ID: ${teamId}`);

    // Create a test user if needed
    const userId = "********-0000-0000-0000-************";
    
    // First check if user exists in auth.users
    const authUserExists = await db.execute(
      sql`SELECT id FROM auth.users WHERE id = ${userId}::uuid`
    );
    
    if (authUserExists.rows.length === 0) {
      console.log("Creating auth user...");
      // Create user in auth.users first
      await db.execute(
        sql`INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role, aud)
           VALUES (${userId}::uuid, '<EMAIL>', NOW(), NOW(), NOW(), '{}', '{}', false, 'authenticated', 'authenticated')`
      );
    }
    
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!existingUser) {
      console.log("Creating test user...");
      await db.insert(users).values({
        id: userId,
        fullName: "Test User",
        email: "<EMAIL>",
        teamId: teamId,
      });

      await db.insert(usersOnTeam).values({
        userId: userId,
        teamId: teamId,
        role: "owner",
      });
    }

    // 1. Seed invoice_templates
    console.log("Checking invoice templates...");
    const existingTemplateResult = await db.execute(
      sql`SELECT id FROM invoice_templates WHERE team_id = ${teamId} LIMIT 1`
    );

    if (existingTemplateResult.rows.length === 0) {
      console.log("Creating invoice template...");
      await db.insert(invoiceTemplates).values({
        teamId: teamId,
        customerLabel: "Customer",
        fromLabel: "From",
        invoiceNoLabel: "Invoice No",
        issueDateLabel: "Issue Date",
        dueDateLabel: "Due Date",
        descriptionLabel: "Description",
        priceLabel: "Price",
        quantityLabel: "Quantity",
        totalLabel: "Total",
        vatLabel: "VAT",
        taxLabel: "Tax",
        paymentLabel: "Payment",
        noteLabel: "Note",
        currency: "USD",
        dateFormat: "MM/DD/YYYY",
        includeVat: false,
        includeTax: false,
        includeDiscount: false,
        includeDecimals: true,
        includeQr: false,
        includeUnits: true,
        subtotalLabel: "Subtotal",
        includePdf: true,
        deliveryType: "create",
      });
    }

    // 2. Seed bank_accounts
    console.log("Checking bank accounts...");
    const existingAccountsResult = await db.execute(
      sql`SELECT id FROM bank_accounts WHERE team_id = ${teamId} LIMIT 1`
    );

    if (existingAccountsResult.rows.length === 0) {
      console.log("Creating bank accounts...");
      await db.insert(bankAccounts).values([
        {
          teamId: teamId,
          createdBy: userId,
          name: "Main Business Account",
          currency: "USD",
          accountId: nanoid(),
          balance: 50000,
          enabled: true,
          manual: true,
        },
        {
          teamId: teamId,
          createdBy: userId,
          name: "Company Credit Card",
          currency: "USD",
          accountId: nanoid(),
          balance: -2500,
          enabled: true,
          manual: true,
        },
      ]);
    }

    // 3. Seed transaction categories
    console.log("Checking transaction categories...");
    const existingCategoriesResult = await db.execute(
      sql`SELECT id FROM transaction_categories WHERE team_id = ${teamId} LIMIT 1`
    );

    if (existingCategoriesResult.rows.length === 0) {
      console.log("Creating transaction categories...");
      const defaultCategories = [
        { name: "Office Supplies", slug: "office_supplies", color: "#3B82F6" },
        { name: "Software", slug: "software", color: "#8B5CF6" },
        { name: "Travel", slug: "travel", color: "#10B981" },
        { name: "Meals", slug: "meals", color: "#F59E0B" },
        { name: "Income", slug: "income", color: "#059669" },
        { name: "Other", slug: "other", color: "#6B7280" },
      ];

      for (const category of defaultCategories) {
        await db.insert(transactionCategories).values({
          teamId: teamId,
          name: category.name,
          slug: category.slug,
          color: category.color,
          system: false,
        });
      }
    }

    // 4. Seed transactions
    console.log("Checking transactions...");
    const existingTransactionsResult = await db.execute(
      sql`SELECT id FROM transactions WHERE team_id = ${teamId} LIMIT 1`
    );

    if (existingTransactionsResult.rows.length === 0) {
      console.log("Creating sample transactions...");
      const accountsResult = await db.execute(
        sql`SELECT id, name FROM bank_accounts WHERE team_id = ${teamId} ORDER BY created_at`
      );

      if (accountsResult.rows.length > 0) {
        const mainAccount = accountsResult.rows[0];
        const creditCard = accountsResult.rows[1];

        const sampleTransactions = [
          {
            teamId: teamId,
            bankAccountId: mainAccount.id,
            date: new Date("2024-01-15"),
            name: "Client Payment - Project Alpha",
            method: "wire" as const,
            amount: 15000,
            currency: "USD",
            internalId: nanoid(),
            status: "posted" as const,
            categorySlug: "income",
            description: "Payment for consulting services",
          },
          {
            teamId: teamId,
            bankAccountId: creditCard?.id,
            date: new Date("2024-01-20"),
            name: "Amazon Web Services",
            method: "card_purchase" as const,
            amount: -523.45,
            currency: "USD",
            internalId: nanoid(),
            status: "posted" as const,
            categorySlug: "software",
            description: "Monthly AWS hosting",
          },
          {
            teamId: teamId,
            bankAccountId: mainAccount.id,
            date: new Date("2024-01-25"),
            name: "Office Rent",
            method: "ach" as const,
            amount: -2500,
            currency: "USD",
            internalId: nanoid(),
            status: "posted" as const,
            categorySlug: "other",
            description: "Monthly office rent payment",
          },
          {
            teamId: teamId,
            bankAccountId: creditCard?.id,
            date: new Date("2024-02-01"),
            name: "Team Lunch",
            method: "card_purchase" as const,
            amount: -145.80,
            currency: "USD",
            internalId: nanoid(),
            status: "posted" as const,
            categorySlug: "meals",
            description: "Team lunch meeting",
          },
          {
            teamId: teamId,
            bankAccountId: mainAccount.id,
            date: new Date("2024-02-05"),
            name: "Client Payment - Project Beta",
            method: "wire" as const,
            amount: 8500,
            currency: "USD",
            internalId: nanoid(),
            status: "posted" as const,
            categorySlug: "income",
            description: "Milestone payment",
          },
        ];

        await db.insert(transactions).values(sampleTransactions);
      }
    }

    console.log("Seed completed successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
    throw error;
  } finally {
    await pool.end();
  }
}

seed().catch((error) => {
  console.error("Failed to seed database:", error);
  process.exit(1);
});