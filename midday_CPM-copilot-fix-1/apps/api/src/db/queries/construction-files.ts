import type { Database } from "@api/db";
import { constructionFiles, users } from "@api/db/schema";
import { buildSearchQuery } from "@api/utils/search";
import { and, desc, eq, gt, sql } from "drizzle-orm";
import type { SQL } from "drizzle-orm/sql/sql";

// Types for parameters
export type GetConstructionFilesParams = {
  teamId: string;
  projectId?: string;
  fileType?: string;
  phase?: string;
  search?: string;
  parentFileId?: string;
  cursor?: string | null;
  pageSize?: number;
};

export type GetConstructionFileByIdParams = {
  teamId: string;
  id: string;
};

export type CreateConstructionFileParams = {
  teamId: string;
  projectId: string;
  uploadedBy: string;
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  mimeType?: string;
  version?: number;
  isLatest?: boolean;
  parentFileId?: string;
  metadata?: any;
  previewPath?: string;
  processedData?: any;
  tags?: string[];
  description?: string;
  relatedPhase?: string;
  accessLevel?: string;
};

export type UpdateConstructionFileParams = {
  teamId: string;
  userId: string;
  id: string;
  fileName?: string;
  description?: string;
  tags?: string[];
  relatedPhase?: string;
  accessLevel?: string;
  metadata?: any;
  processedData?: any;
};

export type DeleteConstructionFileParams = {
  teamId: string;
  id: string;
};

export type ProcessConstructionFileParams = {
  teamId: string;
  userId: string;
  fileId: string;
  metadata?: any;
  processedData?: any;
  previewPath?: string;
};

// Get all construction files for a project or team
export async function getConstructionFiles(
  db: Database,
  params: GetConstructionFilesParams,
) {
  const { 
    teamId, 
    projectId, 
    fileType, 
    phase, 
    search, 
    parentFileId,
    cursor, 
    pageSize = 25 
  } = params;

  const whereConditions: SQL[] = [eq(constructionFiles.teamId, teamId)];

  if (projectId) {
    whereConditions.push(eq(constructionFiles.projectId, projectId));
  }

  if (fileType) {
    whereConditions.push(eq(constructionFiles.fileType, fileType));
  }

  if (phase) {
    whereConditions.push(eq(constructionFiles.relatedPhase, phase));
  }

  if (parentFileId) {
    whereConditions.push(eq(constructionFiles.parentFileId, parentFileId));
  }

  if (search) {
    // Search in file name, description, and tags
    whereConditions.push(
      sql`(
        ${constructionFiles.fileName} ILIKE '%' || ${search} || '%' OR
        ${constructionFiles.originalName} ILIKE '%' || ${search} || '%' OR
        ${constructionFiles.description} ILIKE '%' || ${search} || '%' OR
        array_to_string(${constructionFiles.tags}, ' ') ILIKE '%' || ${search} || '%'
      )`
    );
  }

  if (cursor) {
    whereConditions.push(gt(constructionFiles.id, cursor));
  }

  const files = await db
    .select({
      id: constructionFiles.id,
      createdAt: constructionFiles.createdAt,
      updatedAt: constructionFiles.updatedAt,
      projectId: constructionFiles.projectId,
      teamId: constructionFiles.teamId,
      uploadedBy: constructionFiles.uploadedBy,
      fileName: constructionFiles.fileName,
      originalName: constructionFiles.originalName,
      filePath: constructionFiles.filePath,
      fileSize: constructionFiles.fileSize,
      fileType: constructionFiles.fileType,
      mimeType: constructionFiles.mimeType,
      version: constructionFiles.version,
      isLatest: constructionFiles.isLatest,
      parentFileId: constructionFiles.parentFileId,
      metadata: constructionFiles.metadata,
      previewPath: constructionFiles.previewPath,
      processedData: constructionFiles.processedData,
      tags: constructionFiles.tags,
      description: constructionFiles.description,
      relatedPhase: constructionFiles.relatedPhase,
      accessLevel: constructionFiles.accessLevel,
      uploadedByName: users.fullName,
    })
    .from(constructionFiles)
    .leftJoin(users, eq(constructionFiles.uploadedBy, users.id))
    .where(and(...whereConditions))
    .orderBy(desc(constructionFiles.createdAt))
    .limit(pageSize + 1);

  const hasMore = files.length > pageSize;
  const data = hasMore ? files.slice(0, pageSize) : files;
  const nextCursor = hasMore ? data[data.length - 1]?.id : null;

  return {
    data,
    nextCursor,
    hasMore,
  };
}

// Get a specific construction file by ID
export async function getConstructionFileById(
  db: Database,
  params: GetConstructionFileByIdParams,
) {
  const { teamId, id } = params;

  const file = await db
    .select({
      id: constructionFiles.id,
      createdAt: constructionFiles.createdAt,
      updatedAt: constructionFiles.updatedAt,
      projectId: constructionFiles.projectId,
      teamId: constructionFiles.teamId,
      uploadedBy: constructionFiles.uploadedBy,
      fileName: constructionFiles.fileName,
      originalName: constructionFiles.originalName,
      filePath: constructionFiles.filePath,
      fileSize: constructionFiles.fileSize,
      fileType: constructionFiles.fileType,
      mimeType: constructionFiles.mimeType,
      version: constructionFiles.version,
      isLatest: constructionFiles.isLatest,
      parentFileId: constructionFiles.parentFileId,
      metadata: constructionFiles.metadata,
      previewPath: constructionFiles.previewPath,
      processedData: constructionFiles.processedData,
      tags: constructionFiles.tags,
      description: constructionFiles.description,
      relatedPhase: constructionFiles.relatedPhase,
      accessLevel: constructionFiles.accessLevel,
      uploadedByName: users.fullName,
    })
    .from(constructionFiles)
    .leftJoin(users, eq(constructionFiles.uploadedBy, users.id))
    .where(and(eq(constructionFiles.id, id), eq(constructionFiles.teamId, teamId)))
    .limit(1);

  return file[0] || null;
}

// Create a new construction file record
export async function createConstructionFile(
  db: Database,
  params: CreateConstructionFileParams,
) {
  const {
    teamId,
    projectId,
    uploadedBy,
    fileName,
    originalName,
    filePath,
    fileSize,
    fileType,
    mimeType,
    version = 1,
    isLatest = true,
    parentFileId,
    metadata,
    previewPath,
    processedData,
    tags,
    description,
    relatedPhase,
    accessLevel = "team",
  } = params;

  const [file] = await db
    .insert(constructionFiles)
    .values({
      teamId,
      projectId,
      uploadedBy,
      fileName,
      originalName,
      filePath,
      fileSize,
      fileType,
      mimeType,
      version,
      isLatest,
      parentFileId,
      metadata,
      previewPath,
      processedData,
      tags,
      description,
      relatedPhase,
      accessLevel,
    })
    .returning();

  return file;
}

// Update an existing construction file
export async function updateConstructionFile(
  db: Database,
  params: UpdateConstructionFileParams,
) {
  const {
    teamId,
    id,
    fileName,
    description,
    tags,
    relatedPhase,
    accessLevel,
    metadata,
    processedData,
  } = params;

  const updateData: any = {
    updatedAt: new Date().toISOString(),
  };

  if (fileName !== undefined) updateData.fileName = fileName;
  if (description !== undefined) updateData.description = description;
  if (tags !== undefined) updateData.tags = tags;
  if (relatedPhase !== undefined) updateData.relatedPhase = relatedPhase;
  if (accessLevel !== undefined) updateData.accessLevel = accessLevel;
  if (metadata !== undefined) updateData.metadata = metadata;
  if (processedData !== undefined) updateData.processedData = processedData;

  const [file] = await db
    .update(constructionFiles)
    .set(updateData)
    .where(and(eq(constructionFiles.id, id), eq(constructionFiles.teamId, teamId)))
    .returning();

  return file;
}

// Delete a construction file
export async function deleteConstructionFile(
  db: Database,
  params: DeleteConstructionFileParams,
) {
  const { teamId, id } = params;

  const [file] = await db
    .delete(constructionFiles)
    .where(and(eq(constructionFiles.id, id), eq(constructionFiles.teamId, teamId)))
    .returning();

  return file;
}

// Process a construction file (update metadata and processed data)
export async function processConstructionFile(
  db: Database,
  params: ProcessConstructionFileParams,
) {
  const { teamId, fileId, metadata, processedData, previewPath } = params;

  const updateData: any = {
    updatedAt: new Date().toISOString(),
  };

  if (metadata !== undefined) updateData.metadata = metadata;
  if (processedData !== undefined) updateData.processedData = processedData;
  if (previewPath !== undefined) updateData.previewPath = previewPath;

  const [file] = await db
    .update(constructionFiles)
    .set(updateData)
    .where(and(eq(constructionFiles.id, fileId), eq(constructionFiles.teamId, teamId)))
    .returning();

  return file;
}
