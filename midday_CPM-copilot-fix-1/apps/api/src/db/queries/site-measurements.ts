import type { Database } from "@api/db";
import { siteMeasurements, users } from "@api/db/schema";
import { and, desc, eq, gt, sql } from "drizzle-orm";

// Types for parameters
export type GetSiteMeasurementsParams = {
  teamId: string;
  projectId: string;
  measurementType?: string;
  cursor?: string | null;
  pageSize?: number;
};

export type GetSiteMeasurementByIdParams = {
  teamId: string;
  id: string;
};

export type CreateSiteMeasurementParams = {
  teamId: string;
  userId: string;
  projectId: string;
  measurementType: string;
  measuredBy: string;
  areaGeometry?: any;
  elevationData?: any;
  cutVolume?: number;
  fillVolume?: number;
  totalArea?: number;
  perimeter?: number;
  accuracy?: number;
  equipment?: string;
  notes?: string;
  attachments?: string[];
};

export type UpdateSiteMeasurementParams = {
  teamId: string;
  userId: string;
  id: string;
  measurementType?: string;
  areaGeometry?: any;
  elevationData?: any;
  cutVolume?: number;
  fillVolume?: number;
  totalArea?: number;
  perimeter?: number;
  accuracy?: number;
  equipment?: string;
  notes?: string;
  attachments?: string[];
};

export type DeleteSiteMeasurementParams = {
  teamId: string;
  id: string;
};

// Get all site measurements for a project
export async function getSiteMeasurements(
  db: Database,
  params: GetSiteMeasurementsParams,
) {
  const { teamId, projectId, measurementType, cursor, pageSize = 25 } = params;

  const whereConditions = [
    eq(siteMeasurements.teamId, teamId),
    eq(siteMeasurements.projectId, projectId),
  ];

  if (measurementType) {
    whereConditions.push(eq(siteMeasurements.measurementType, measurementType));
  }

  if (cursor) {
    whereConditions.push(gt(siteMeasurements.id, cursor));
  }

  const measurements = await db
    .select({
      id: siteMeasurements.id,
      createdAt: siteMeasurements.createdAt,
      projectId: siteMeasurements.projectId,
      teamId: siteMeasurements.teamId,
      userId: siteMeasurements.userId,
      measurementType: siteMeasurements.measurementType,
      areaGeometry: siteMeasurements.areaGeometry,
      elevationData: siteMeasurements.elevationData,
      cutVolume: siteMeasurements.cutVolume,
      fillVolume: siteMeasurements.fillVolume,
      totalArea: siteMeasurements.totalArea,
      perimeter: siteMeasurements.perimeter,
      measurementDate: siteMeasurements.measurementDate,
      measuredBy: siteMeasurements.measuredBy,
      accuracy: siteMeasurements.accuracy,
      equipment: siteMeasurements.equipment,
      notes: siteMeasurements.notes,
      attachments: siteMeasurements.attachments,
      measuredByName: users.fullName,
    })
    .from(siteMeasurements)
    .leftJoin(users, eq(siteMeasurements.measuredBy, users.id))
    .where(and(...whereConditions))
    .orderBy(desc(siteMeasurements.createdAt))
    .limit(pageSize + 1);

  const hasMore = measurements.length > pageSize;
  const data = hasMore ? measurements.slice(0, pageSize) : measurements;
  const nextCursor = hasMore ? data[data.length - 1]?.id : null;

  return {
    data,
    nextCursor,
    hasMore,
  };
}

// Get a specific site measurement by ID
export async function getSiteMeasurementById(
  db: Database,
  params: GetSiteMeasurementByIdParams,
) {
  const { teamId, id } = params;

  const measurement = await db
    .select({
      id: siteMeasurements.id,
      createdAt: siteMeasurements.createdAt,
      projectId: siteMeasurements.projectId,
      teamId: siteMeasurements.teamId,
      userId: siteMeasurements.userId,
      measurementType: siteMeasurements.measurementType,
      areaGeometry: siteMeasurements.areaGeometry,
      elevationData: siteMeasurements.elevationData,
      cutVolume: siteMeasurements.cutVolume,
      fillVolume: siteMeasurements.fillVolume,
      totalArea: siteMeasurements.totalArea,
      perimeter: siteMeasurements.perimeter,
      measurementDate: siteMeasurements.measurementDate,
      measuredBy: siteMeasurements.measuredBy,
      accuracy: siteMeasurements.accuracy,
      equipment: siteMeasurements.equipment,
      notes: siteMeasurements.notes,
      attachments: siteMeasurements.attachments,
      measuredByName: users.fullName,
    })
    .from(siteMeasurements)
    .leftJoin(users, eq(siteMeasurements.measuredBy, users.id))
    .where(and(eq(siteMeasurements.id, id), eq(siteMeasurements.teamId, teamId)))
    .limit(1);

  return measurement[0] || null;
}

// Create a new site measurement
export async function createSiteMeasurement(
  db: Database,
  params: CreateSiteMeasurementParams,
) {
  const {
    teamId,
    userId,
    projectId,
    measurementType,
    measuredBy,
    areaGeometry,
    elevationData,
    cutVolume,
    fillVolume,
    totalArea,
    perimeter,
    accuracy,
    equipment,
    notes,
    attachments,
  } = params;

  const [measurement] = await db
    .insert(siteMeasurements)
    .values({
      teamId,
      userId,
      projectId,
      measurementType,
      measuredBy,
      areaGeometry,
      elevationData,
      cutVolume: cutVolume?.toString(),
      fillVolume: fillVolume?.toString(),
      totalArea: totalArea?.toString(),
      perimeter: perimeter?.toString(),
      measurementDate: new Date().toISOString(),
      accuracy: accuracy?.toString(),
      equipment,
      notes,
      attachments,
    })
    .returning();

  return measurement;
}

// Update an existing site measurement
export async function updateSiteMeasurement(
  db: Database,
  params: UpdateSiteMeasurementParams,
) {
  const {
    teamId,
    id,
    measurementType,
    areaGeometry,
    elevationData,
    cutVolume,
    fillVolume,
    totalArea,
    perimeter,
    accuracy,
    equipment,
    notes,
    attachments,
  } = params;

  const updateData: any = {};

  if (measurementType !== undefined) updateData.measurementType = measurementType;
  if (areaGeometry !== undefined) updateData.areaGeometry = areaGeometry;
  if (elevationData !== undefined) updateData.elevationData = elevationData;
  if (cutVolume !== undefined) updateData.cutVolume = cutVolume.toString();
  if (fillVolume !== undefined) updateData.fillVolume = fillVolume.toString();
  if (totalArea !== undefined) updateData.totalArea = totalArea.toString();
  if (perimeter !== undefined) updateData.perimeter = perimeter.toString();
  if (accuracy !== undefined) updateData.accuracy = accuracy.toString();
  if (equipment !== undefined) updateData.equipment = equipment;
  if (notes !== undefined) updateData.notes = notes;
  if (attachments !== undefined) updateData.attachments = attachments;

  const [measurement] = await db
    .update(siteMeasurements)
    .set(updateData)
    .where(and(eq(siteMeasurements.id, id), eq(siteMeasurements.teamId, teamId)))
    .returning();

  return measurement;
}

// Delete a site measurement
export async function deleteSiteMeasurement(
  db: Database,
  params: DeleteSiteMeasurementParams,
) {
  const { teamId, id } = params;

  const [measurement] = await db
    .delete(siteMeasurements)
    .where(and(eq(siteMeasurements.id, id), eq(siteMeasurements.teamId, teamId)))
    .returning();

  return measurement;
}
