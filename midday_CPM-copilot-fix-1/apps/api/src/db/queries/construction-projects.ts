import type { Database } from "@api/db";
import {
  constructionAnnotations,
  constructionProgressUpdates,
  constructionProjects,
  customers,
  users,
} from "@api/db/schema";
import { buildSearchQuery } from "@api/utils/search";
import { and, asc, desc, eq, gte, inArray, lte, sql } from "drizzle-orm";
import type { SQL } from "drizzle-orm/sql/sql";

export type ConstructionProjectStatus = 
  | "planning"
  | "in_progress" 
  | "on_hold"
  | "completed"
  | "cancelled";

export type ConstructionPhase = 
  | "site_preparation"
  | "foundation"
  | "framing"
  | "roofing"
  | "electrical"
  | "plumbing"
  | "insulation"
  | "drywall"
  | "flooring"
  | "painting"
  | "final_inspection";

export type GetConstructionProjectsParams = {
  teamId: string;
  cursor?: string | null;
  pageSize?: number;
  q?: string | null;
  start?: string | null;
  end?: string | null;
  status?: ConstructionProjectStatus | null;
  customers?: string[] | null;
  phase?: ConstructionPhase | null;
  sort?: string[] | null;
};

export type UpsertConstructionProjectParams = {
  teamId: string;
  userId: string;
  id?: string;
  name: string;
  description?: string | null;
  location?: string | null;
  address?: string | null;
  startDate?: string | null;
  endDate?: string | null;
  estimatedCost?: number | null;
  actualCost?: number | null;
  currency?: string | null;
  completionPercentage?: number;
  siteArea?: number | null;
  buildingArea?: number | null;
  currentPhase?: ConstructionPhase | null;
  status?: ConstructionProjectStatus;
  customerId?: string | null;
  contractorInfo?: Record<string, any> | null;
  permitInfo?: Record<string, any> | null;
  siteCoordinates?: Record<string, any> | null;
};

export type DeleteConstructionProjectParams = {
  teamId: string;
  id: string;
};

export type GetConstructionProjectByIdParams = {
  teamId: string;
  id: string;
};

export type CreateProgressUpdateParams = {
  teamId: string;
  userId: string;
  projectId: string;
  phase?: ConstructionPhase | null;
  progressPercentage: number;
  description?: string | null;
  notes?: string | null;
  workCompleted?: string | null;
  nextSteps?: string | null;
  weatherConditions?: string | null;
  workersOnSite?: number | null;
  equipmentUsed?: Record<string, any> | null;
  issuesReported?: string | null;
};

export type CreateAnnotationParams = {
  teamId: string;
  userId: string;
  projectId: string;
  type: string;
  content: string;
  position: Record<string, any>;
  relatedPhase?: ConstructionPhase | null;
  priority?: string;
  assignedTo?: string | null;
};

export async function getConstructionProjects(
  db: Database,
  params: GetConstructionProjectsParams,
) {
  const {
    teamId,
    sort,
    cursor,
    pageSize = 25,
    q,
    status,
    start,
    end,
    customers: customerIds,
    phase,
  } = params;

  const whereConditions: SQL[] = [eq(constructionProjects.teamId, teamId)];

  // Apply status filter
  if (status) {
    whereConditions.push(eq(constructionProjects.status, status));
  }

  // Apply phase filter
  if (phase) {
    whereConditions.push(eq(constructionProjects.currentPhase, phase));
  }

  // Apply date range filter
  if (start && end) {
    whereConditions.push(gte(constructionProjects.createdAt, start));
    whereConditions.push(lte(constructionProjects.createdAt, end));
  }

  // Apply customer filter  
  if (customerIds && customerIds.length > 0) {
    whereConditions.push(inArray(constructionProjects.customerId, customerIds));
  }

  // Apply search query filter
  if (q) {
    const query = buildSearchQuery(q);
    whereConditions.push(
      sql`to_tsquery('english', ${query}) @@ ${constructionProjects.fts}`,
    );
  }

  // Start building the query
  const query = db
    .select({
      id: constructionProjects.id,
      name: constructionProjects.name,
      description: constructionProjects.description,
      status: constructionProjects.status,
      location: constructionProjects.location,
      address: constructionProjects.address,
      startDate: constructionProjects.startDate,
      endDate: constructionProjects.endDate,
      estimatedCost: constructionProjects.estimatedCost,
      actualCost: constructionProjects.actualCost,
      currency: constructionProjects.currency,
      completionPercentage: constructionProjects.completionPercentage,
      siteArea: constructionProjects.siteArea,
      buildingArea: constructionProjects.buildingArea,
      currentPhase: constructionProjects.currentPhase,
      customerId: constructionProjects.customerId,
      contractorInfo: constructionProjects.contractorInfo,
      permitInfo: constructionProjects.permitInfo,
      siteCoordinates: constructionProjects.siteCoordinates,
      teamId: constructionProjects.teamId,
      createdAt: constructionProjects.createdAt,
      updatedAt: constructionProjects.updatedAt,
      createdBy: constructionProjects.createdBy,
    })
    .from(constructionProjects)
    .where(and(...whereConditions))
    .leftJoin(customers, eq(constructionProjects.customerId, customers.id));

  // Apply sorting
  if (sort && sort.length > 0) {
    const orderExpressions = sort.map((sortField) => {
      const [field, direction] = sortField.startsWith("-")
        ? [sortField.slice(1), "desc"]
        : [sortField, "asc"];

      const column = constructionProjects[field as keyof typeof constructionProjects];
      return direction === "desc" ? desc(column) : asc(column);
    });
    query.orderBy(...orderExpressions);
  } else {
    query.orderBy(desc(constructionProjects.createdAt));
  }

  // Apply pagination
  const offset = cursor ? Number.parseInt(cursor, 10) : 0;
  query.limit(pageSize).offset(offset);

  // Execute query to get projects
  const projectsData = await query;

  // Get customer data for each project
  const data = projectsData.map((project) => ({
    ...project,
    customer: project.customerId
      ? {
          id: project.customerId,
          name: "", // Will be filled from customer join
          website: "",
        }
      : null,
  }));

  // Calculate next cursor
  const nextCursor =
    data.length === pageSize ? (offset + pageSize).toString() : undefined;

  return {
    meta: {
      cursor: nextCursor,
      hasPreviousPage: offset > 0,
      hasNextPage: data.length === pageSize,
    },
    data,
  };
}

export async function upsertConstructionProject(
  db: Database,
  params: UpsertConstructionProjectParams,
) {
  const { teamId, userId, id, ...projectData } = params;

  if (id) {
    // Update existing project
    const [result] = await db
      .update(constructionProjects)
      .set({
        ...projectData,
        updatedAt: new Date().toISOString(),
      })
      .where(
        and(eq(constructionProjects.id, id), eq(constructionProjects.teamId, teamId))
      )
      .returning();

    return result;
  } else {
    // Create new project
    const [result] = await db
      .insert(constructionProjects)
      .values({
        ...projectData,
        teamId,
        createdBy: userId,
        status: projectData.status || "planning",
        completionPercentage: projectData.completionPercentage || 0,
      })
      .returning();

    return result;
  }
}

export async function deleteConstructionProject(
  db: Database,
  params: DeleteConstructionProjectParams,
) {
  const { teamId, id } = params;

  const [result] = await db
    .delete(constructionProjects)
    .where(
      and(eq(constructionProjects.id, id), eq(constructionProjects.teamId, teamId))
    )
    .returning({ id: constructionProjects.id });

  return result;
}

export async function getConstructionProjectById(
  db: Database,
  params: GetConstructionProjectByIdParams,
) {
  const { teamId, id } = params;

  const [project] = await db
    .select({
      id: constructionProjects.id,
      name: constructionProjects.name,
      description: constructionProjects.description,
      status: constructionProjects.status,
      location: constructionProjects.location,
      address: constructionProjects.address,
      startDate: constructionProjects.startDate,
      endDate: constructionProjects.endDate,
      estimatedCost: constructionProjects.estimatedCost,
      actualCost: constructionProjects.actualCost,
      currency: constructionProjects.currency,
      completionPercentage: constructionProjects.completionPercentage,
      siteArea: constructionProjects.siteArea,
      buildingArea: constructionProjects.buildingArea,
      currentPhase: constructionProjects.currentPhase,
      customerId: constructionProjects.customerId,
      contractorInfo: constructionProjects.contractorInfo,
      permitInfo: constructionProjects.permitInfo,
      siteCoordinates: constructionProjects.siteCoordinates,
      teamId: constructionProjects.teamId,
      createdAt: constructionProjects.createdAt,
      updatedAt: constructionProjects.updatedAt,
      createdBy: constructionProjects.createdBy,
      customerName: customers.name,
      customerWebsite: customers.website,
    })
    .from(constructionProjects)
    .leftJoin(customers, eq(constructionProjects.customerId, customers.id))
    .where(
      and(eq(constructionProjects.id, id), eq(constructionProjects.teamId, teamId))
    );

  if (!project) {
    return null;
  }

  // Get recent progress updates
  const progressUpdates = await db
    .select({
      id: constructionProgressUpdates.id,
      createdAt: constructionProgressUpdates.createdAt,
      progressPercentage: constructionProgressUpdates.progressPercentage,
      description: constructionProgressUpdates.description,
      phase: constructionProgressUpdates.phase,
      userId: constructionProgressUpdates.userId,
      userName: users.fullName,
    })
    .from(constructionProgressUpdates)
    .leftJoin(users, eq(constructionProgressUpdates.userId, users.id))
    .where(eq(constructionProgressUpdates.projectId, id))
    .orderBy(desc(constructionProgressUpdates.createdAt))
    .limit(10);

  // Get active annotations
  const annotations = await db
    .select({
      id: constructionAnnotations.id,
      type: constructionAnnotations.type,
      content: constructionAnnotations.content,
      position: constructionAnnotations.position,
      status: constructionAnnotations.status,
      createdAt: constructionAnnotations.createdAt,
      userId: constructionAnnotations.userId,
      userName: users.fullName,
    })
    .from(constructionAnnotations)
    .leftJoin(users, eq(constructionAnnotations.userId, users.id))
    .where(
      and(
        eq(constructionAnnotations.projectId, id),
        eq(constructionAnnotations.status, "active")
      )
    )
    .orderBy(desc(constructionAnnotations.createdAt))
    .limit(50);

  return {
    ...project,
    customer: project.customerId
      ? {
          id: project.customerId,
          name: project.customerName || "",
          website: project.customerWebsite || "",
        }
      : null,
    progressUpdates,
    annotations,
  };
}

export async function createProgressUpdate(
  db: Database,
  params: CreateProgressUpdateParams,
) {
  const { teamId, userId, projectId, ...updateData } = params;

  const [result] = await db
    .insert(constructionProgressUpdates)
    .values({
      ...updateData,
      projectId,
      teamId,
      userId,
    })
    .returning();

  // Update project completion percentage if provided
  if (updateData.progressPercentage !== undefined) {
    await db
      .update(constructionProjects)
      .set({
        completionPercentage: updateData.progressPercentage,
        updatedAt: new Date().toISOString(),
      })
      .where(
        and(
          eq(constructionProjects.id, projectId),
          eq(constructionProjects.teamId, teamId)
        )
      );
  }

  return result;
}

export async function createAnnotation(
  db: Database,
  params: CreateAnnotationParams,
) {
  const { teamId, userId, projectId, ...annotationData } = params;

  const [result] = await db
    .insert(constructionAnnotations)
    .values({
      ...annotationData,
      projectId,
      teamId,
      userId,
      priority: annotationData.priority || "normal",
      status: "active",
    })
    .returning();

  return result;
}

export async function getProjectProgressUpdates(
  db: Database,
  params: { teamId: string; projectId: string; limit?: number },
) {
  const { teamId, projectId, limit = 50 } = params;

  const updates = await db
    .select({
      id: constructionProgressUpdates.id,
      createdAt: constructionProgressUpdates.createdAt,
      phase: constructionProgressUpdates.phase,
      progressPercentage: constructionProgressUpdates.progressPercentage,
      description: constructionProgressUpdates.description,
      notes: constructionProgressUpdates.notes,
      workCompleted: constructionProgressUpdates.workCompleted,
      nextSteps: constructionProgressUpdates.nextSteps,
      weatherConditions: constructionProgressUpdates.weatherConditions,
      workersOnSite: constructionProgressUpdates.workersOnSite,
      equipmentUsed: constructionProgressUpdates.equipmentUsed,
      issuesReported: constructionProgressUpdates.issuesReported,
      photos: constructionProgressUpdates.photos,
      userId: constructionProgressUpdates.userId,
      userName: users.fullName,
      userAvatar: users.avatarUrl,
    })
    .from(constructionProgressUpdates)
    .leftJoin(users, eq(constructionProgressUpdates.userId, users.id))
    .where(
      and(
        eq(constructionProgressUpdates.projectId, projectId),
        eq(constructionProgressUpdates.teamId, teamId)
      )
    )
    .orderBy(desc(constructionProgressUpdates.createdAt))
    .limit(limit);

  return updates;
}

export async function getProjectAnnotations(
  db: Database,
  params: { teamId: string; projectId: string; status?: string },
) {
  const { teamId, projectId, status = "active" } = params;

  const whereConditions = [
    eq(constructionAnnotations.projectId, projectId),
    eq(constructionAnnotations.teamId, teamId),
  ];

  if (status) {
    whereConditions.push(eq(constructionAnnotations.status, status));
  }

  const annotations = await db
    .select({
      id: constructionAnnotations.id,
      type: constructionAnnotations.type,
      content: constructionAnnotations.content,
      position: constructionAnnotations.position,
      status: constructionAnnotations.status,
      relatedPhase: constructionAnnotations.relatedPhase,
      priority: constructionAnnotations.priority,
      attachments: constructionAnnotations.attachments,
      createdAt: constructionAnnotations.createdAt,
      resolvedAt: constructionAnnotations.resolvedAt,
      userId: constructionAnnotations.userId,
      userName: users.fullName,
      userAvatar: users.avatarUrl,
      assignedTo: constructionAnnotations.assignedTo,
      assignedToName: sql<string>`assigned_user.full_name`,
      resolvedBy: constructionAnnotations.resolvedBy,
      resolvedByName: sql<string>`resolved_user.full_name`,
    })
    .from(constructionAnnotations)
    .leftJoin(users, eq(constructionAnnotations.userId, users.id))
    .leftJoin(
      sql`${users} as assigned_user`,
      eq(constructionAnnotations.assignedTo, sql`assigned_user.id`)
    )
    .leftJoin(
      sql`${users} as resolved_user`,
      eq(constructionAnnotations.resolvedBy, sql`resolved_user.id`)
    )
    .where(and(...whereConditions))
    .orderBy(desc(constructionAnnotations.createdAt));

  return annotations;
}