import { z } from "@hono/zod-openapi";

// File type enum schema
export const fileTypeSchema = z.enum([
  "cad",
  "bim",
  "survey",
  "photo",
  "document",
  "video",
  "model_3d",
  "drawing",
  "specification",
]);

// Construction phase enum schema
export const constructionPhaseSchema = z.enum([
  "site_preparation",
  "foundation",
  "framing",
  "roofing",
  "electrical",
  "plumbing",
  "insulation",
  "drywall",
  "flooring",
  "painting",
  "final_inspection",
]);

// Get construction files schema
export const getConstructionFilesSchema = z.object({
  projectId: z.string().uuid().optional().openapi({
    description: "ID of the construction project to filter files",
    example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
  }),
  fileType: fileTypeSchema.optional().openapi({
    description: "Filter by file type",
    example: "cad",
  }),
  phase: constructionPhaseSchema.optional().openapi({
    description: "Filter by construction phase",
    example: "framing",
  }),
  search: z.string().optional().openapi({
    description: "Search query for file names, descriptions, or tags",
    example: "floor plan",
  }),
  parentFileId: z.string().uuid().optional().openapi({
    description: "Filter by parent file ID to get versions",
    example: "c8f7d6e5-2a1b-4c3d-9e8f-1234567890ab",
  }),
  cursor: z.string().nullable().optional().openapi({
    description: "Cursor for pagination",
    example: "eyJpZCI6IjEyMyJ9",
  }),
  pageSize: z.coerce.number().min(1).max(100).optional().default(25).openapi({
    description: "Number of files to return per page (1-100)",
    example: 25,
  }),
});

// Get construction file by ID schema
export const getConstructionFileByIdSchema = z.object({
  id: z.string().uuid().openapi({
    description: "Unique identifier of the construction file",
    example: "c8f7d6e5-2a1b-4c3d-9e8f-1234567890ab",
  }),
});

// Create construction file schema
export const createConstructionFileSchema = z.object({
  projectId: z.string().uuid().openapi({
    description: "ID of the construction project",
    example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
  }),
  fileName: z.string().min(1).openapi({
    description: "Name of the file",
    example: "floor-plan-v2.dwg",
  }),
  originalName: z.string().min(1).openapi({
    description: "Original name of the uploaded file",
    example: "Floor Plan Version 2.dwg",
  }),
  filePath: z.string().min(1).openapi({
    description: "Storage path of the file",
    example: "construction-files/team-123/project-456/floor-plan-v2.dwg",
  }),
  fileSize: z.number().min(0).openapi({
    description: "Size of the file in bytes",
    example: 2048576,
  }),
  fileType: fileTypeSchema.openapi({
    description: "Type of the construction file",
    example: "cad",
  }),
  mimeType: z.string().optional().openapi({
    description: "MIME type of the file",
    example: "application/acad",
  }),
  version: z.number().min(1).optional().default(1).openapi({
    description: "Version number of the file",
    example: 2,
  }),
  isLatest: z.boolean().optional().default(true).openapi({
    description: "Whether this is the latest version",
    example: true,
  }),
  parentFileId: z.string().uuid().optional().openapi({
    description: "ID of the parent file for versioning",
    example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
  }),
  metadata: z.record(z.any()).optional().openapi({
    description: "CAD/BIM specific metadata",
    example: { layers: ["foundation", "walls"], scale: "1:100" },
  }),
  tags: z.array(z.string()).optional().openapi({
    description: "Tags for categorizing the file",
    example: ["architectural", "floor-plan", "residential"],
  }),
  description: z.string().optional().openapi({
    description: "Description of the file",
    example: "Updated floor plan with revised kitchen layout",
  }),
  relatedPhase: constructionPhaseSchema.optional().openapi({
    description: "Construction phase this file relates to",
    example: "framing",
  }),
  accessLevel: z.enum(["public", "team", "restricted"]).optional().default("team").openapi({
    description: "Access level for the file",
    example: "team",
  }),
});

// Update construction file schema
export const updateConstructionFileSchema = z.object({
  id: z.string().uuid().openapi({
    description: "Unique identifier of the construction file to update",
    example: "c8f7d6e5-2a1b-4c3d-9e8f-1234567890ab",
  }),
  fileName: z.string().min(1).optional().openapi({
    description: "Updated name of the file",
    example: "floor-plan-v3.dwg",
  }),
  description: z.string().optional().openapi({
    description: "Updated description of the file",
    example: "Final floor plan with all revisions incorporated",
  }),
  tags: z.array(z.string()).optional().openapi({
    description: "Updated tags for the file",
    example: ["architectural", "floor-plan", "final"],
  }),
  relatedPhase: constructionPhaseSchema.optional().openapi({
    description: "Updated construction phase",
    example: "foundation",
  }),
  accessLevel: z.enum(["public", "team", "restricted"]).optional().openapi({
    description: "Updated access level",
    example: "restricted",
  }),
  metadata: z.record(z.any()).optional().openapi({
    description: "Updated metadata",
    example: { layers: ["foundation", "walls", "electrical"], scale: "1:50" },
  }),
});

// Delete construction file schema
export const deleteConstructionFileSchema = z.object({
  id: z.string().uuid().openapi({
    description: "Unique identifier of the construction file to delete",
    example: "c8f7d6e5-2a1b-4c3d-9e8f-1234567890ab",
  }),
});

// Process file schema
export const processFileSchema = z.object({
  fileId: z.string().uuid().openapi({
    description: "ID of the file to process",
    example: "c8f7d6e5-2a1b-4c3d-9e8f-1234567890ab",
  }),
  metadata: z.record(z.any()).optional().openapi({
    description: "Extracted metadata from file processing",
    example: { 
      dimensions: { width: 1000, height: 800 },
      layers: ["foundation", "walls"],
      entities: 150
    },
  }),
  processedData: z.record(z.any()).optional().openapi({
    description: "Processed data from CAD/BIM analysis",
    example: {
      quantityTakeoffs: { concrete: 50, steel: 25 },
      areas: { total: 2500, living: 2000 }
    },
  }),
  previewPath: z.string().optional().openapi({
    description: "Path to generated preview/thumbnail",
    example: "previews/construction-files/floor-plan-preview.jpg",
  }),
});

// Response schemas
export const constructionFileResponseSchema = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string().nullable(),
  projectId: z.string().uuid(),
  teamId: z.string().uuid(),
  uploadedBy: z.string().uuid(),
  fileName: z.string(),
  originalName: z.string(),
  filePath: z.string(),
  fileSize: z.number(),
  fileType: fileTypeSchema,
  mimeType: z.string().nullable(),
  version: z.number().nullable(),
  isLatest: z.boolean().nullable(),
  parentFileId: z.string().uuid().nullable(),
  metadata: z.record(z.any()).nullable(),
  previewPath: z.string().nullable(),
  processedData: z.record(z.any()).nullable(),
  tags: z.array(z.string()).nullable(),
  description: z.string().nullable(),
  relatedPhase: constructionPhaseSchema.nullable(),
  accessLevel: z.string().nullable(),
  uploadedByName: z.string().nullable(),
});

export const constructionFilesResponseSchema = z.object({
  data: z.array(constructionFileResponseSchema),
  nextCursor: z.string().nullable(),
  hasMore: z.boolean(),
});
