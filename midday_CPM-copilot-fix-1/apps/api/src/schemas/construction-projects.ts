import { z } from "@hono/zod-openapi";

export const getConstructionProjectsSchema = z.object({
  cursor: z
    .string()
    .nullable()
    .optional()
    .openapi({
      description:
        "Cursor for pagination, representing the last item from the previous page",
      example: "eyJpZCI6IjEyMyJ9",
      param: {
        in: "query",
      },
    }),
  pageSize: z.coerce
    .number()
    .min(1)
    .max(100)
    .optional()
    .openapi({
      description: "Number of projects to return per page (1-100)",
      example: 20,
      param: {
        in: "query",
      },
    }),
  q: z
    .string()
    .nullable()
    .optional()
    .openapi({
      description:
        "Search query string to filter projects by name, description, or location",
      example: "office building",
      param: {
        in: "query",
      },
    }),
  start: z
    .string()
    .nullable()
    .optional()
    .openapi({
      description:
        "Start date for filtering projects by creation date in YYYY-MM-DD format",
      example: "2024-04-01",
      param: {
        in: "query",
      },
    }),
  end: z
    .string()
    .nullable()
    .optional()
    .openapi({
      description:
        "End date for filtering projects by creation date in YYYY-MM-DD format",
      example: "2024-04-30",
      param: {
        in: "query",
      },
    }),
  status: z
    .enum(["planning", "in_progress", "on_hold", "completed", "cancelled"])
    .nullable()
    .optional()
    .openapi({
      description: "Filter projects by status",
      example: "in_progress",
      param: {
        in: "query",
      },
    }),
  customers: z
    .array(z.string())
    .nullable()
    .optional()
    .openapi({
      description:
        "Array of customer IDs to filter projects by specific customers",
      example: ["customer-1", "customer-2"],
      param: {
        in: "query",
      },
    }),
  phase: z
    .enum([
      "site_preparation",
      "foundation",
      "framing",
      "roofing",
      "electrical",
      "plumbing",
      "insulation",
      "drywall",
      "flooring",
      "painting",
      "final_inspection",
    ])
    .nullable()
    .optional()
    .openapi({
      description: "Filter projects by current construction phase",
      example: "framing",
      param: {
        in: "query",
      },
    }),
  sort: z
    .array(z.string())
    .nullable()
    .optional()
    .openapi({
      description:
        "Sorting order as an array of field names. Prefix with '-' for descending order",
      example: ["-createdAt", "name"],
      param: {
        in: "query",
      },
    }),
});

export const upsertConstructionProjectSchema = z
  .object({
    id: z.string().uuid().optional().openapi({
      description:
        "Unique identifier for the project. Required for updates, omit for new projects",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    name: z.string().min(1).openapi({
      description: "Name of the construction project",
      example: "Downtown Office Building",
    }),
    description: z.string().nullable().optional().openapi({
      description: "Detailed description of the project",
      example:
        "Construction of a 12-story office building with underground parking and modern amenities",
    }),
    location: z.string().nullable().optional().openapi({
      description: "General location of the project",
      example: "Downtown, New York",
    }),
    address: z.string().nullable().optional().openapi({
      description: "Specific address of the project site",
      example: "123 Main Street, New York, NY 10001",
    }),
    startDate: z.string().nullable().optional().openapi({
      description: "Planned start date in ISO 8601 format",
      example: "2024-06-01T08:00:00.000Z",
    }),
    endDate: z.string().nullable().optional().openapi({
      description: "Planned completion date in ISO 8601 format",
      example: "2025-12-31T17:00:00.000Z",
    }),
    estimatedCost: z.number().nullable().optional().openapi({
      description: "Estimated total cost of the project",
      example: 2500000.00,
    }),
    actualCost: z.number().nullable().optional().openapi({
      description: "Actual cost incurred so far",
      example: 500000.00,
    }),
    currency: z.string().nullable().optional().openapi({
      description: "Currency code in ISO 4217 format",
      example: "USD",
    }),
    completionPercentage: z.number().min(0).max(100).optional().default(0).openapi({
      description: "Overall completion percentage (0-100)",
      example: 25.5,
    }),
    siteArea: z.number().nullable().optional().openapi({
      description: "Total site area in square feet or square meters",
      example: 50000.0,
    }),
    buildingArea: z.number().nullable().optional().openapi({
      description: "Total building area in square feet or square meters",
      example: 180000.0,
    }),
    currentPhase: z
      .enum([
        "site_preparation",
        "foundation",
        "framing",
        "roofing",
        "electrical",
        "plumbing",
        "insulation",
        "drywall",
        "flooring",
        "painting",
        "final_inspection",
      ])
      .nullable()
      .optional()
      .openapi({
        description: "Current construction phase",
        example: "framing",
      }),
    status: z
      .enum(["planning", "in_progress", "on_hold", "completed", "cancelled"])
      .optional()
      .openapi({
        description: "Current status of the project",
        example: "in_progress",
      }),
    customerId: z.string().uuid().nullable().optional().openapi({
      description:
        "Unique identifier of the customer/client associated with this project",
      example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
    }),
    contractorInfo: z
      .object({
        name: z.string().optional(),
        contact: z.string().optional(),
        license: z.string().optional(),
        insurance: z.string().optional(),
      })
      .nullable()
      .optional()
      .openapi({
        description: "Information about the main contractor",
        example: {
          name: "ABC Construction",
          contact: "<EMAIL>",
          license: "LIC123456",
          insurance: "INS789012",
        },
      }),
    permitInfo: z
      .object({
        number: z.string().optional(),
        issued: z.string().optional(),
        expires: z.string().optional(),
        type: z.string().optional(),
      })
      .nullable()
      .optional()
      .openapi({
        description: "Building permit information",
        example: {
          number: "PERMIT-2024-001",
          issued: "2024-05-15",
          expires: "2025-05-15",
          type: "Commercial Construction",
        },
      }),
    siteCoordinates: z
      .object({
        latitude: z.number().optional(),
        longitude: z.number().optional(),
        elevation: z.number().optional(),
      })
      .nullable()
      .optional()
      .openapi({
        description: "GPS coordinates and elevation of the site",
        example: {
          latitude: 40.7589,
          longitude: -73.9851,
          elevation: 15.2,
        },
      }),
  })
  .openapi("UpsertConstructionProject");

export const deleteConstructionProjectSchema = z.object({
  id: z.string().uuid().openapi({
    description: "Unique identifier of the project to delete",
    example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
  }),
});

export const getConstructionProjectByIdSchema = z.object({
  id: z.string().uuid().openapi({
    description: "Unique identifier of the project to retrieve",
    example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
  }),
});

export const constructionProjectResponseSchema = z
  .object({
    id: z.string().uuid().openapi({
      description: "Unique identifier of the project",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    name: z.string().openapi({
      description: "Name of the construction project",
      example: "Downtown Office Building",
    }),
    description: z.string().nullable().openapi({
      description: "Detailed description of the project",
      example:
        "Construction of a 12-story office building with underground parking and modern amenities",
    }),
    status: z
      .enum(["planning", "in_progress", "on_hold", "completed", "cancelled"])
      .openapi({
        description: "Current status of the project",
        example: "in_progress",
      }),
    location: z.string().nullable().openapi({
      description: "General location of the project",
      example: "Downtown, New York",
    }),
    address: z.string().nullable().openapi({
      description: "Specific address of the project site",
      example: "123 Main Street, New York, NY 10001",
    }),
    startDate: z.string().nullable().openapi({
      description: "Planned start date in ISO 8601 format",
      example: "2024-06-01T08:00:00.000Z",
    }),
    endDate: z.string().nullable().openapi({
      description: "Planned completion date in ISO 8601 format",
      example: "2025-12-31T17:00:00.000Z",
    }),
    estimatedCost: z.number().nullable().openapi({
      description: "Estimated total cost of the project",
      example: 2500000.00,
    }),
    actualCost: z.number().nullable().openapi({
      description: "Actual cost incurred so far",
      example: 500000.00,
    }),
    currency: z.string().nullable().openapi({
      description: "Currency code in ISO 4217 format",
      example: "USD",
    }),
    completionPercentage: z.number().openapi({
      description: "Overall completion percentage (0-100)",
      example: 25.5,
    }),
    siteArea: z.number().nullable().openapi({
      description: "Total site area in square feet or square meters",
      example: 50000.0,
    }),
    buildingArea: z.number().nullable().openapi({
      description: "Total building area in square feet or square meters",
      example: 180000.0,
    }),
    currentPhase: z
      .enum([
        "site_preparation",
        "foundation",
        "framing",
        "roofing",
        "electrical",
        "plumbing",
        "insulation",
        "drywall",
        "flooring",
        "painting",
        "final_inspection",
      ])
      .nullable()
      .openapi({
        description: "Current construction phase",
        example: "framing",
      }),
    createdAt: z.string().openapi({
      description:
        "Date and time when the project was created in ISO 8601 format",
      example: "2024-05-01T12:00:00.000Z",
    }),
    updatedAt: z.string().nullable().openapi({
      description:
        "Date and time when the project was last updated in ISO 8601 format",
      example: "2024-05-15T14:30:00.000Z",
    }),
    customer: z
      .object({
        id: z.string().uuid().openapi({
          description: "Unique identifier of the customer",
          example: "a1b2c3d4-e5f6-7890-abcd-1234567890ef",
        }),
        name: z.string().openapi({
          description: "Name of the customer or organization",
          example: "Acme Corporation",
        }),
        website: z.string().openapi({
          description: "Website URL of the customer",
          example: "https://acme.com",
        }),
      })
      .nullable()
      .openapi({
        description: "Customer information associated with the project",
      }),
    contractorInfo: z
      .object({
        name: z.string().nullable(),
        contact: z.string().nullable(),
        license: z.string().nullable(),
        insurance: z.string().nullable(),
      })
      .nullable()
      .openapi({
        description: "Information about the main contractor",
      }),
    permitInfo: z
      .object({
        number: z.string().nullable(),
        issued: z.string().nullable(),
        expires: z.string().nullable(),
        type: z.string().nullable(),
      })
      .nullable()
      .openapi({
        description: "Building permit information",
      }),
    siteCoordinates: z
      .object({
        latitude: z.number().nullable(),
        longitude: z.number().nullable(),
        elevation: z.number().nullable(),
      })
      .nullable()
      .openapi({
        description: "GPS coordinates and elevation of the site",
      }),
    progressUpdates: z
      .array(
        z.object({
          id: z.string().uuid(),
          createdAt: z.string(),
          progressPercentage: z.number(),
          description: z.string().nullable(),
          phase: z.string().nullable(),
          userId: z.string().uuid(),
          userName: z.string(),
        }),
      )
      .nullable()
      .openapi({
        description: "Recent progress updates for the project",
      }),
    annotations: z
      .array(
        z.object({
          id: z.string().uuid(),
          type: z.string(),
          content: z.string(),
          position: z.object({}).passthrough(),
          status: z.string(),
          createdAt: z.string(),
          userId: z.string().uuid(),
          userName: z.string(),
        }),
      )
      .nullable()
      .openapi({
        description: "Active annotations for the project",
      }),
  })
  .openapi("ConstructionProjectResponse");

export const constructionProjectsResponseSchema = z
  .object({
    meta: z
      .object({
        hasNextPage: z.boolean().openapi({
          description:
            "Whether there are more projects available on the next page",
          example: true,
        }),
        hasPreviousPage: z.boolean().openapi({
          description:
            "Whether there are more projects available on the previous page",
          example: false,
        }),
      })
      .openapi({
        description: "Pagination metadata for the projects response",
      }),
    data: z.array(constructionProjectResponseSchema).openapi({
      description: "Array of construction projects matching the query criteria",
    }),
  })
  .openapi("ConstructionProjectsResponse");

// Progress update schemas
export const createProgressUpdateSchema = z
  .object({
    projectId: z.string().uuid().openapi({
      description: "ID of the construction project",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    phase: z
      .enum([
        "site_preparation",
        "foundation",
        "framing",
        "roofing",
        "electrical",
        "plumbing",
        "insulation",
        "drywall",
        "flooring",
        "painting",
        "final_inspection",
      ])
      .nullable()
      .optional()
      .openapi({
        description: "Construction phase this update relates to",
        example: "framing",
      }),
    progressPercentage: z.number().min(0).max(100).openapi({
      description: "Progress percentage for this update (0-100)",
      example: 75.5,
    }),
    description: z.string().nullable().optional().openapi({
      description: "Description of work completed",
      example: "Completed steel frame for floors 1-3",
    }),
    notes: z.string().nullable().optional().openapi({
      description: "Additional notes or observations",
      example: "Weather was favorable, ahead of schedule",
    }),
    workCompleted: z.string().nullable().optional().openapi({
      description: "Specific work items completed",
      example: "Steel beams installed, concrete poured",
    }),
    nextSteps: z.string().nullable().optional().openapi({
      description: "Planned next steps",
      example: "Begin electrical rough-in next week",
    }),
    weatherConditions: z.string().nullable().optional().openapi({
      description: "Weather conditions during work",
      example: "Sunny, 75°F, light breeze",
    }),
    workersOnSite: z.number().nullable().optional().openapi({
      description: "Number of workers on site",
      example: 12,
    }),
    equipmentUsed: z
      .array(
        z.object({
          name: z.string(),
          type: z.string(),
          hours: z.number().optional(),
        }),
      )
      .nullable()
      .optional()
      .openapi({
        description: "Equipment used during this work session",
        example: [
          { name: "Crane #1", type: "tower_crane", hours: 8 },
          { name: "Excavator", type: "excavator", hours: 6 },
        ],
      }),
    issuesReported: z.string().nullable().optional().openapi({
      description: "Any issues or problems encountered",
      example: "Minor delay due to delivery of materials",
    }),
  })
  .openapi("CreateProgressUpdate");

// Annotation schemas
export const createAnnotationSchema = z
  .object({
    projectId: z.string().uuid().openapi({
      description: "ID of the construction project",
      example: "b7e6c8e2-1f2a-4c3b-9e2d-1a2b3c4d5e6f",
    }),
    type: z.enum(["note", "issue", "measurement", "photo"]).openapi({
      description: "Type of annotation",
      example: "issue",
    }),
    content: z.string().min(1).openapi({
      description: "Content of the annotation",
      example: "Crack noticed in foundation wall - needs inspection",
    }),
    position: z
      .object({
        x: z.number(),
        y: z.number(),
        z: z.number().optional(),
      })
      .openapi({
        description: "3D position coordinates for the annotation",
        example: { x: 125.5, y: 200.0, z: 15.2 },
      }),
    relatedPhase: z
      .enum([
        "site_preparation",
        "foundation",
        "framing",
        "roofing",
        "electrical",
        "plumbing",
        "insulation",
        "drywall",
        "flooring",
        "painting",
        "final_inspection",
      ])
      .nullable()
      .optional()
      .openapi({
        description: "Construction phase this annotation relates to",
        example: "foundation",
      }),
    priority: z
      .enum(["low", "normal", "high", "critical"])
      .optional()
      .default("normal")
      .openapi({
        description: "Priority level of the annotation",
        example: "high",
      }),
    assignedTo: z.string().uuid().nullable().optional().openapi({
      description: "User ID to assign this annotation to",
      example: "f1e2d3c4-b5a6-7890-abcd-1234567890ef",
    }),
  })
  .openapi("CreateAnnotation");