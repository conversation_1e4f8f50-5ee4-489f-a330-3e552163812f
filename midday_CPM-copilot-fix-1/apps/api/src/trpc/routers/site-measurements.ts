import {
  createSiteMeasurement,
  deleteSiteMeasurement,
  getSiteMeasurements,
  getSiteMeasurementById,
  updateSiteMeasurement,
} from "@api/db/queries/site-measurements";
import {
  createSiteMeasurementSchema,
  deleteSiteMeasurementSchema,
  getSiteMeasurementsSchema,
  getSiteMeasurementByIdSchema,
  updateSiteMeasurementSchema,
} from "@api/schemas/site-measurements";
import { createTRPCRouter, protectedProcedure } from "@api/trpc/init";
import { z } from "zod";

export const siteMeasurementsRouter = createTRPCRouter({
  // Get all site measurements for a project
  get: protectedProcedure
    .input(getSiteMeasurementsSchema)
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getSiteMeasurements(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Get a specific site measurement by ID
  getById: protectedProcedure
    .input(getSiteMeasurementByIdSchema)
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getSiteMeasurementById(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Get site measurements by project ID
  getByProject: protectedProcedure
    .input(z.object({ projectId: z.string().uuid() }))
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getSiteMeasurements(db, {
        projectId: input.projectId,
        teamId: teamId!,
      });
    }),

  // Create a new site measurement
  create: protectedProcedure
    .input(createSiteMeasurementSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return createSiteMeasurement(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
        measuredBy: user.id,
      });
    }),

  // Update an existing site measurement
  update: protectedProcedure
    .input(updateSiteMeasurementSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return updateSiteMeasurement(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
      });
    }),

  // Delete a site measurement
  delete: protectedProcedure
    .input(deleteSiteMeasurementSchema)
    .mutation(async ({ input, ctx: { db, teamId } }) => {
      return deleteSiteMeasurement(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Calculate cut and fill volumes for a project
  calculateVolumes: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid(),
        originalElevation: z.number(),
        targetElevation: z.number(),
        areaGeometry: z.object({
          type: z.literal("Polygon"),
          coordinates: z.array(z.array(z.array(z.number()))),
        }),
      })
    )
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      // Calculate cut and fill volumes based on elevation data
      const { projectId, originalElevation, targetElevation, areaGeometry } = input;
      
      // Simple volume calculation (in a real implementation, this would use more sophisticated algorithms)
      const area = calculatePolygonArea(areaGeometry.coordinates[0]);
      const elevationDifference = targetElevation - originalElevation;
      const volume = Math.abs(area * elevationDifference);
      
      const cutVolume = elevationDifference < 0 ? volume : 0;
      const fillVolume = elevationDifference > 0 ? volume : 0;

      // Create a measurement record
      return createSiteMeasurement(db, {
        projectId,
        teamId: teamId!,
        userId: user.id,
        measuredBy: user.id,
        measurementType: "cut_fill",
        areaGeometry,
        cutVolume,
        fillVolume,
        totalArea: area,
        notes: `Calculated volumes: Cut ${cutVolume.toFixed(2)} m³, Fill ${fillVolume.toFixed(2)} m³`,
      });
    }),

  // Get measurement statistics for a project
  getStatistics: protectedProcedure
    .input(z.object({ projectId: z.string().uuid() }))
    .query(async ({ input, ctx: { db, teamId } }) => {
      const measurements = await getSiteMeasurements(db, {
        projectId: input.projectId,
        teamId: teamId!,
      });

      const stats = {
        totalMeasurements: measurements.length,
        totalArea: measurements.reduce((sum, m) => sum + (m.totalArea || 0), 0),
        totalCutVolume: measurements.reduce((sum, m) => sum + (m.cutVolume || 0), 0),
        totalFillVolume: measurements.reduce((sum, m) => sum + (m.fillVolume || 0), 0),
        measurementTypes: measurements.reduce((acc, m) => {
          acc[m.measurementType] = (acc[m.measurementType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        averageAccuracy: measurements.length > 0 
          ? measurements.reduce((sum, m) => sum + (m.accuracy || 0), 0) / measurements.length
          : 0,
      };

      return stats;
    }),
});

// Helper function to calculate polygon area using the shoelace formula
function calculatePolygonArea(coordinates: number[][]): number {
  let area = 0;
  const n = coordinates.length;
  
  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }
  
  return Math.abs(area) / 2;
}
