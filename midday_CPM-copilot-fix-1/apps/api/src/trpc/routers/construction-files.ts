import {
  createConstructionFile,
  deleteConstructionFile,
  getConstructionFiles,
  getConstructionFileById,
  updateConstructionFile,
  processConstructionFile,
} from "@api/db/queries/construction-files";
import { constructionFiles } from "@api/db/schema";
import { and, eq } from "drizzle-orm";
import {
  createConstructionFileSchema,
  deleteConstructionFileSchema,
  getConstructionFilesSchema,
  getConstructionFileByIdSchema,
  updateConstructionFileSchema,
  processFileSchema,
} from "@api/schemas/construction-files";
import { createTRPCRouter, protectedProcedure } from "@api/trpc/init";
import { signedUrl } from "@midday/supabase/storage";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

export const constructionFilesRouter = createTRPCRouter({
  // Get all construction files for a project
  get: protectedProcedure
    .input(getConstructionFilesSchema)
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getConstructionFiles(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Get a specific construction file by ID
  getById: protectedProcedure
    .input(getConstructionFileByIdSchema)
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getConstructionFileById(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Create a new construction file record
  create: protectedProcedure
    .input(createConstructionFileSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return createConstructionFile(db, {
        ...input,
        teamId: teamId!,
        uploadedBy: user.id,
      });
    }),

  // Update an existing construction file
  update: protectedProcedure
    .input(updateConstructionFileSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return updateConstructionFile(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
      });
    }),

  // Delete a construction file
  delete: protectedProcedure
    .input(deleteConstructionFileSchema)
    .mutation(async ({ input, ctx: { db, teamId } }) => {
      return deleteConstructionFile(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Get signed URL for file upload
  getUploadUrl: protectedProcedure
    .input(
      z.object({
        fileName: z.string(),
        fileType: z.string(),
        projectId: z.string().uuid(),
      })
    )
    .mutation(async ({ input, ctx: { teamId } }) => {
      const { fileName, fileType, projectId } = input;
      
      // Generate a unique file path
      const timestamp = Date.now();
      const filePath = `construction-files/${teamId}/${projectId}/${timestamp}-${fileName}`;
      
      try {
        const { data, error } = await signedUrl({
          bucket: "construction-files",
          path: filePath,
          options: {
            upsert: true,
          },
        });

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to generate upload URL",
          });
        }

        return {
          uploadUrl: data.signedUrl,
          filePath,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate upload URL",
        });
      }
    }),

  // Get signed URL for file download
  getDownloadUrl: protectedProcedure
    .input(z.object({ filePath: z.string() }))
    .mutation(async ({ input }) => {
      const { filePath } = input;
      
      try {
        const { data, error } = await signedUrl({
          bucket: "construction-files",
          path: filePath,
          options: {
            download: true,
            expiresIn: 3600, // 1 hour
          },
        });

        if (error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to generate download URL",
          });
        }

        return {
          downloadUrl: data.signedUrl,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate download URL",
        });
      }
    }),

  // Process uploaded file (extract metadata, generate previews)
  processFile: protectedProcedure
    .input(processFileSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return processConstructionFile(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
      });
    }),

  // Get file versions
  getVersions: protectedProcedure
    .input(z.object({ fileId: z.string().uuid() }))
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getConstructionFiles(db, {
        teamId: teamId!,
        parentFileId: input.fileId,
      });
    }),

  // Create new version of existing file
  createVersion: protectedProcedure
    .input(
      z.object({
        parentFileId: z.string().uuid(),
        fileName: z.string(),
        filePath: z.string(),
        fileSize: z.number(),
        fileType: z.string(),
        mimeType: z.string().optional(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      // Get parent file to inherit project and other details
      const parentFile = await getConstructionFileById(db, {
        id: input.parentFileId,
        teamId: teamId!,
      });

      if (!parentFile) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Parent file not found",
        });
      }

      // Mark previous versions as not latest
      await db
        .update(constructionFiles)
        .set({ isLatest: false })
        .where(
          and(
            eq(constructionFiles.parentFileId, input.parentFileId),
            eq(constructionFiles.teamId, teamId!)
          )
        );

      // Create new version
      return createConstructionFile(db, {
        projectId: parentFile.projectId,
        teamId: teamId!,
        uploadedBy: user.id,
        fileName: input.fileName,
        originalName: input.fileName,
        filePath: input.filePath,
        fileSize: input.fileSize,
        fileType: input.fileType,
        mimeType: input.mimeType,
        parentFileId: input.parentFileId,
        version: (parentFile.version || 1) + 1,
        isLatest: true,
        description: input.description,
        relatedPhase: parentFile.relatedPhase,
        accessLevel: parentFile.accessLevel,
      });
    }),

  // Search files by content or metadata
  search: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid().optional(),
        query: z.string(),
        fileType: z.string().optional(),
        phase: z.string().optional(),
        limit: z.number().min(1).max(100).optional().default(25),
      })
    )
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getConstructionFiles(db, {
        teamId: teamId!,
        projectId: input.projectId,
        search: input.query,
        fileType: input.fileType,
        phase: input.phase,
        pageSize: input.limit,
      });
    }),
});
