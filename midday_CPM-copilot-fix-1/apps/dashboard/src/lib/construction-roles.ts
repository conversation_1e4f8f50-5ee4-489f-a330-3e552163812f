// Construction project role definitions and permissions

export type ConstructionRole = 
  | "project_manager"
  | "site_engineer" 
  | "architect"
  | "contractor"
  | "subcontractor"
  | "client"
  | "inspector"
  | "safety_officer"
  | "foreman"
  | "worker";

export type Permission = 
  | "view_project"
  | "edit_project"
  | "delete_project"
  | "manage_team"
  | "upload_files"
  | "delete_files"
  | "add_comments"
  | "edit_comments"
  | "update_progress"
  | "approve_milestones"
  | "view_financials"
  | "edit_financials"
  | "create_reports"
  | "manage_schedule"
  | "safety_oversight"
  | "quality_control"
  | "client_communication";

// Role-based permissions matrix
export const ROLE_PERMISSIONS: Record<ConstructionRole, Permission[]> = {
  project_manager: [
    "view_project",
    "edit_project", 
    "delete_project",
    "manage_team",
    "upload_files",
    "delete_files",
    "add_comments",
    "edit_comments",
    "update_progress",
    "approve_milestones",
    "view_financials",
    "edit_financials",
    "create_reports",
    "manage_schedule",
    "client_communication"
  ],
  
  site_engineer: [
    "view_project",
    "edit_project",
    "upload_files",
    "add_comments",
    "edit_comments",
    "update_progress",
    "create_reports",
    "quality_control",
    "manage_schedule"
  ],
  
  architect: [
    "view_project",
    "edit_project",
    "upload_files",
    "add_comments",
    "edit_comments",
    "create_reports",
    "quality_control"
  ],
  
  contractor: [
    "view_project",
    "upload_files",
    "add_comments",
    "update_progress",
    "create_reports",
    "manage_schedule"
  ],
  
  subcontractor: [
    "view_project",
    "upload_files",
    "add_comments",
    "update_progress"
  ],
  
  client: [
    "view_project",
    "add_comments",
    "view_financials",
    "client_communication"
  ],
  
  inspector: [
    "view_project",
    "upload_files",
    "add_comments",
    "quality_control",
    "create_reports"
  ],
  
  safety_officer: [
    "view_project",
    "upload_files",
    "add_comments",
    "safety_oversight",
    "create_reports"
  ],
  
  foreman: [
    "view_project",
    "upload_files",
    "add_comments",
    "update_progress",
    "manage_schedule"
  ],
  
  worker: [
    "view_project",
    "add_comments",
    "update_progress"
  ]
};

// Role display information
export const ROLE_INFO: Record<ConstructionRole, { 
  label: string; 
  description: string; 
  color: string;
  icon: string;
}> = {
  project_manager: {
    label: "Project Manager",
    description: "Overall project oversight and management",
    color: "bg-blue-500",
    icon: "👨‍💼"
  },
  site_engineer: {
    label: "Site Engineer", 
    description: "Technical oversight and engineering support",
    color: "bg-green-500",
    icon: "👷‍♂️"
  },
  architect: {
    label: "Architect",
    description: "Design and architectural oversight",
    color: "bg-purple-500", 
    icon: "📐"
  },
  contractor: {
    label: "Contractor",
    description: "Primary construction contractor",
    color: "bg-orange-500",
    icon: "🔨"
  },
  subcontractor: {
    label: "Subcontractor",
    description: "Specialized trade contractor",
    color: "bg-yellow-500",
    icon: "🔧"
  },
  client: {
    label: "Client",
    description: "Project owner and stakeholder",
    color: "bg-indigo-500",
    icon: "🏢"
  },
  inspector: {
    label: "Inspector",
    description: "Quality and compliance inspector",
    color: "bg-red-500",
    icon: "🔍"
  },
  safety_officer: {
    label: "Safety Officer",
    description: "Safety oversight and compliance",
    color: "bg-red-600",
    icon: "⚠️"
  },
  foreman: {
    label: "Foreman",
    description: "On-site crew supervisor",
    color: "bg-gray-600",
    icon: "👷"
  },
  worker: {
    label: "Worker",
    description: "Construction worker",
    color: "bg-gray-500",
    icon: "👨‍🔧"
  }
};

// Permission checking utilities
export function hasPermission(userRole: ConstructionRole, permission: Permission): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
}

export function canUserAccess(userRole: ConstructionRole, requiredPermissions: Permission[]): boolean {
  const userPermissions = ROLE_PERMISSIONS[userRole] || [];
  return requiredPermissions.every(permission => userPermissions.includes(permission));
}

export function getUserPermissions(userRole: ConstructionRole): Permission[] {
  return ROLE_PERMISSIONS[userRole] || [];
}

export function getRoleInfo(role: ConstructionRole) {
  return ROLE_INFO[role];
}

// Context-aware permission checking
export function getContextualPermissions(
  userRole: ConstructionRole, 
  context: {
    isProjectOwner?: boolean;
    isTeamMember?: boolean;
    projectPhase?: string;
  }
): Permission[] {
  let permissions = getUserPermissions(userRole);
  
  // Project owners get additional permissions
  if (context.isProjectOwner) {
    permissions = [...permissions, "delete_project", "manage_team"];
  }
  
  // Team members get basic collaboration permissions
  if (context.isTeamMember) {
    permissions = [...permissions, "add_comments", "view_project"];
  }
  
  // Phase-specific permissions
  if (context.projectPhase === "planning" && userRole === "architect") {
    permissions = [...permissions, "edit_project"];
  }
  
  // Remove duplicates
  return [...new Set(permissions)];
}

// UI helper functions
export function shouldShowFeature(userRole: ConstructionRole, feature: string): boolean {
  const featurePermissions: Record<string, Permission[]> = {
    "financial_dashboard": ["view_financials"],
    "team_management": ["manage_team"],
    "project_settings": ["edit_project"],
    "file_upload": ["upload_files"],
    "progress_updates": ["update_progress"],
    "milestone_approval": ["approve_milestones"],
    "safety_reports": ["safety_oversight"],
    "quality_control": ["quality_control"]
  };
  
  const requiredPermissions = featurePermissions[feature];
  if (!requiredPermissions) return true; // Show by default if no restrictions
  
  return canUserAccess(userRole, requiredPermissions);
}

export function getFilteredMenuItems(userRole: ConstructionRole) {
  const allMenuItems = [
    { id: "overview", label: "Overview", permissions: ["view_project"] },
    { id: "progress", label: "Progress", permissions: ["view_project"] },
    { id: "files", label: "Files", permissions: ["view_project"] },
    { id: "team", label: "Team", permissions: ["view_project"] },
    { id: "financials", label: "Financials", permissions: ["view_financials"] },
    { id: "reports", label: "Reports", permissions: ["create_reports"] },
    { id: "settings", label: "Settings", permissions: ["edit_project"] }
  ];
  
  return allMenuItems.filter(item => 
    canUserAccess(userRole, item.permissions as Permission[])
  );
}
