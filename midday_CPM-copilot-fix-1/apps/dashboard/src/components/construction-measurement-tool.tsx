"use client";

import { useState, useRef, useCallback } from "react";
import { use<PERSON>rame, useThree } from "@react-three/fiber";
import { Html, Line, Text } from "@react-three/drei";
import * as THREE from "three";
import { Button } from "@midday/ui/button";
import { Badge } from "@midday/ui/badge";
import { Ruler, X, Check } from "lucide-react";

interface MeasurementPoint {
  id: string;
  position: THREE.Vector3;
  screenPosition?: { x: number; y: number };
}

interface Measurement {
  id: string;
  startPoint: MeasurementPoint;
  endPoint: MeasurementPoint;
  distance: number;
  label: string;
}

interface Props {
  isActive: boolean;
  onMeasurementComplete?: (measurement: Measurement) => void;
  onMeasurementCancel?: () => void;
}

export function ConstructionMeasurementTool({ 
  isActive, 
  onMeasurementComplete, 
  onMeasurementCancel 
}: Props) {
  const [measurements, setMeasurements] = useState<Measurement[]>([]);
  const [currentMeasurement, setCurrentMeasurement] = useState<{
    startPoint: MeasurementPoint | null;
    endPoint: MeasurementPoint | null;
  }>({ startPoint: null, endPoint: null });
  const [isDrawing, setIsDrawing] = useState(false);
  
  const { camera, raycaster, scene } = useThree();
  const mouseRef = useRef(new THREE.Vector2());

  // Handle mouse clicks for measurement points
  const handleClick = useCallback((event: MouseEvent) => {
    if (!isActive) return;

    // Convert mouse position to normalized device coordinates
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // Raycast to find intersection with scene objects
    raycaster.setFromCamera(mouseRef.current, camera);
    const intersects = raycaster.intersectObjects(scene.children, true);

    if (intersects.length > 0) {
      const point = intersects[0].point;
      const newPoint: MeasurementPoint = {
        id: `point_${Date.now()}`,
        position: point.clone(),
        screenPosition: { x: event.clientX, y: event.clientY }
      };

      if (!currentMeasurement.startPoint) {
        // First point
        setCurrentMeasurement({ startPoint: newPoint, endPoint: null });
        setIsDrawing(true);
      } else if (!currentMeasurement.endPoint) {
        // Second point - complete measurement
        const distance = currentMeasurement.startPoint.position.distanceTo(point);
        const measurement: Measurement = {
          id: `measurement_${Date.now()}`,
          startPoint: currentMeasurement.startPoint,
          endPoint: newPoint,
          distance,
          label: `${distance.toFixed(2)}m`
        };

        setMeasurements(prev => [...prev, measurement]);
        onMeasurementComplete?.(measurement);
        setCurrentMeasurement({ startPoint: null, endPoint: null });
        setIsDrawing(false);
      }
    }
  }, [isActive, currentMeasurement, camera, raycaster, scene, onMeasurementComplete]);

  // Handle mouse movement for live preview
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isActive || !isDrawing || !currentMeasurement.startPoint) return;

    const rect = (event.target as HTMLElement).getBoundingClientRect();
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    raycaster.setFromCamera(mouseRef.current, camera);
    const intersects = raycaster.intersectObjects(scene.children, true);

    if (intersects.length > 0) {
      const point = intersects[0].point;
      setCurrentMeasurement(prev => ({
        ...prev,
        endPoint: {
          id: 'temp',
          position: point.clone(),
          screenPosition: { x: event.clientX, y: event.clientY }
        }
      }));
    }
  }, [isActive, isDrawing, currentMeasurement.startPoint, camera, raycaster, scene]);

  // Cancel current measurement
  const cancelMeasurement = () => {
    setCurrentMeasurement({ startPoint: null, endPoint: null });
    setIsDrawing(false);
    onMeasurementCancel?.();
  };

  // Clear all measurements
  const clearAllMeasurements = () => {
    setMeasurements([]);
    cancelMeasurement();
  };

  // Render measurement lines and labels
  const renderMeasurements = () => {
    const allMeasurements = [...measurements];
    
    // Add current measurement if in progress
    if (currentMeasurement.startPoint && currentMeasurement.endPoint) {
      const distance = currentMeasurement.startPoint.position.distanceTo(
        currentMeasurement.endPoint.position
      );
      allMeasurements.push({
        id: 'current',
        startPoint: currentMeasurement.startPoint,
        endPoint: currentMeasurement.endPoint,
        distance,
        label: `${distance.toFixed(2)}m`
      });
    }

    return allMeasurements.map((measurement) => (
      <group key={measurement.id}>
        {/* Measurement line */}
        <Line
          points={[measurement.startPoint.position, measurement.endPoint.position]}
          color={measurement.id === 'current' ? "#FFA500" : "#FF0000"}
          lineWidth={3}
        />
        
        {/* Start point marker */}
        <mesh position={measurement.startPoint.position}>
          <sphereGeometry args={[0.2]} />
          <meshBasicMaterial color={measurement.id === 'current' ? "#FFA500" : "#FF0000"} />
        </mesh>
        
        {/* End point marker */}
        <mesh position={measurement.endPoint.position}>
          <sphereGeometry args={[0.2]} />
          <meshBasicMaterial color={measurement.id === 'current' ? "#FFA500" : "#FF0000"} />
        </mesh>
        
        {/* Distance label */}
        <Html
          position={[
            (measurement.startPoint.position.x + measurement.endPoint.position.x) / 2,
            (measurement.startPoint.position.y + measurement.endPoint.position.y) / 2 + 1,
            (measurement.startPoint.position.z + measurement.endPoint.position.z) / 2
          ]}
          center
        >
          <Badge 
            variant="secondary" 
            className={`bg-white shadow-lg ${
              measurement.id === 'current' ? 'border-orange-500' : 'border-red-500'
            }`}
          >
            <Ruler className="h-3 w-3 mr-1" />
            {measurement.label}
          </Badge>
        </Html>
      </group>
    ));
  };

  // Attach event listeners
  useFrame(() => {
    if (isActive) {
      const canvas = document.querySelector('canvas');
      if (canvas) {
        canvas.addEventListener('click', handleClick);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.style.cursor = 'crosshair';
        
        return () => {
          canvas.removeEventListener('click', handleClick);
          canvas.removeEventListener('mousemove', handleMouseMove);
          canvas.style.cursor = 'default';
        };
      }
    }
  });

  if (!isActive) return null;

  return (
    <>
      {/* Render all measurements */}
      {renderMeasurements()}
      
      {/* Measurement UI overlay */}
      <Html fullscreen>
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 flex items-center space-x-2">
            <Ruler className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium">
              {isDrawing ? 'Click to set end point' : 'Click to start measuring'}
            </span>
            
            {measurements.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllMeasurements}
                className="h-7"
              >
                Clear All
              </Button>
            )}
            
            {isDrawing && (
              <Button
                variant="outline"
                size="sm"
                onClick={cancelMeasurement}
                className="h-7"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
        
        {/* Measurements list */}
        {measurements.length > 0 && (
          <div className="absolute top-20 right-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 max-w-xs">
              <h3 className="text-sm font-medium mb-2">Measurements</h3>
              <div className="space-y-1">
                {measurements.map((measurement, index) => (
                  <div key={measurement.id} className="flex items-center justify-between text-xs">
                    <span>Measurement {index + 1}</span>
                    <Badge variant="outline">{measurement.label}</Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Html>
    </>
  );
}
