"use client";

import { <PERSON><PERSON> } from "@midday/ui/button";
import { Badge } from "@midday/ui/badge";
import { Progress } from "@midday/ui/progress";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@midday/ui/collapsible";
import { ArrowLeft, ChevronDown, Users, AlertTriangle } from "lucide-react";
import { useState } from "react";

interface ProjectDetailsProps {
  project: any;
  siteMeasurements: any[];
  onBack: () => void;
  userRole?: any;
  permissions?: any;
}

const statusColors = {
  planning: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  in_progress: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300", 
  on_hold: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

export function ConstructionProjectDetails({ project, siteMeasurements, onBack, userRole, permissions }: ProjectDetailsProps) {
  const [isDetailsOpen, setIsDetailsOpen] = useState(true);
  const [isNotesOpen, setIsNotesOpen] = useState(false);

  const completion = project.completionPercentage || 0;
  const latestMeasurement = siteMeasurements?.[0];

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
          </Button>
          <span className="text-sm text-muted-foreground ml-2">Projects</span>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-orange-500 rounded-sm" />
            <span className="text-sm text-muted-foreground">
              {project.location || "Kometsu - South Harbor"}
            </span>
          </div>
          
          <h1 className="text-xl font-bold">
            {project.name || "Smart Construction"}
          </h1>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <span className="text-2xl font-bold text-orange-500">{completion.toFixed(0)}%</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              <span className="text-sm">24</span>
            </div>
            <div className="flex items-center gap-1">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <span className="text-sm">2</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Details Section */}
        <Collapsible open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-gray-50 dark:hover:bg-gray-800">
            <span className="font-medium">Details</span>
            <ChevronDown className={`h-4 w-4 transition-transform ${isDetailsOpen ? 'rotate-180' : ''}`} />
          </CollapsibleTrigger>
          <CollapsibleContent className="px-4 pb-4">
            <div className="space-y-4">
              {/* Team Members */}
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center text-xs text-white font-medium">
                    JD
                  </div>
                  <div className="w-8 h-8 bg-green-500 rounded-full border-2 border-white flex items-center justify-center text-xs text-white font-medium">
                    SM
                  </div>
                  <div className="w-8 h-8 bg-purple-500 rounded-full border-2 border-white flex items-center justify-center text-xs text-white font-medium">
                    +2
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">72%</span>
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <span className="text-sm text-muted-foreground">Alerts</span>
                <span className="text-sm text-muted-foreground">54%</span>
              </div>

              {/* 2D Cross Section */}
              <div className="space-y-2">
                <h3 className="font-medium">2D Cross Section</h3>
                
                {/* Progress Bar */}
                <div className="space-y-1">
                  <div className="text-4xl font-bold">54%</div>
                  <Progress value={54} className="h-2" />
                </div>

                {/* Elevation Chart */}
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <div className="grid grid-cols-6 gap-2 text-xs text-center mb-2">
                    <span>0</span>
                    <span>20</span>
                    <span>40</span>
                    <span>60</span>
                    <span>80</span>
                    <span>100</span>
                  </div>
                  
                  {/* Simple elevation visualization */}
                  <div className="h-16 bg-gradient-to-r from-orange-200 via-orange-400 to-red-400 rounded relative">
                    <div className="absolute inset-0 bg-gradient-to-t from-orange-600/20 to-transparent rounded" />
                  </div>
                </div>
              </div>

              {/* Cut and Fill Section */}
              <Collapsible>
                <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
                  <span className="font-medium">Cut and Fill</span>
                  <ChevronDown className="h-4 w-4" />
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-3">
                  <div className="text-sm text-muted-foreground">
                    17 Dec, 2020 11:59PM
                  </div>
                  
                  {/* Elevation Data */}
                  <div className="grid grid-cols-5 gap-2 text-xs">
                    <div className="text-center">
                      <div className="font-medium">-20 ft</div>
                      <div className="text-muted-foreground">0.00 ft</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium">-15 ft</div>
                      <div className="text-muted-foreground">14 ft</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium">-9.77 ft</div>
                      <div className="text-muted-foreground">28 ft</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium">-6.68 ft</div>
                      <div className="text-muted-foreground">41 ft</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium">0.00 ft</div>
                      <div className="text-muted-foreground">55 ft</div>
                    </div>
                  </div>

                  {/* Volume Calculations */}
                  <div className="space-y-2 pt-2 border-t">
                    <div className="flex justify-between text-sm">
                      <span>Cut:</span>
                      <span className="font-medium">
                        {latestMeasurement?.cutVolume?.toLocaleString() || "250,395"} cu yd
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Fill:</span>
                      <span className="font-medium">
                        {latestMeasurement?.fillVolume?.toLocaleString() || "23,995"} cu yd
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Net:</span>
                      <span className="font-medium">226,400 cu yd</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Minimum height difference:</span>
                      <span className="font-medium">0.00 in</span>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Notes Section */}
        <Collapsible open={isNotesOpen} onOpenChange={setIsNotesOpen}>
          <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-gray-50 dark:hover:bg-gray-800 border-t">
            <span className="font-medium">Notes</span>
            <ChevronDown className={`h-4 w-4 transition-transform ${isNotesOpen ? 'rotate-180' : ''}`} />
          </CollapsibleTrigger>
          <CollapsibleContent className="px-4 pb-4">
            <div className="text-sm text-muted-foreground">
              Project notes and updates will appear here...
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}
