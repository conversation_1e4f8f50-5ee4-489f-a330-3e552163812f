"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@midday/ui/card";
import { Badge } from "@midday/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@midday/ui/avatar";
import { <PERSON><PERSON><PERSON><PERSON> } from "@midday/ui/scroll-area";
import { 
  Activity, 
  Upload, 
  MessageSquare, 
  CheckCircle, 
  AlertTriangle, 
  Users,
  Camera,
  FileText,
  MapPin
} from "lucide-react";
import { useState, useEffect } from "react";

interface ActivityFeedProps {
  projectId: string;
}

// Mock activity data - in real app this would come from Supabase realtime
const mockActivities = [
  {
    id: "1",
    type: "progress_update",
    userId: "user-1",
    userName: "<PERSON>",
    userAvatar: null,
    userInitials: "SM",
    action: "updated progress",
    target: "Foundation Phase",
    details: "Progress increased to 85%",
    timestamp: new Date(Date.now() - 1000 * 60 * 2), // 2 minutes ago
    metadata: { progress: 85, previousProgress: 80 }
  },
  {
    id: "2",
    type: "file_upload",
    userId: "user-2",
    userName: "<PERSON>",
    userAvatar: null,
    userInitials: "JD",
    action: "uploaded",
    target: "Site Photos",
    details: "3 new inspection photos",
    timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    metadata: { fileCount: 3, fileType: "images" }
  },
  {
    id: "3",
    type: "comment",
    userId: "user-3",
    userName: "Mike Johnson",
    userAvatar: null,
    userInitials: "MJ",
    action: "commented on",
    target: "Electrical Plans",
    details: "Needs revision on circuit layout",
    timestamp: new Date(Date.now() - 1000 * 60 * 8), // 8 minutes ago
    metadata: { commentType: "revision_request" }
  },
  {
    id: "4",
    type: "milestone",
    userId: "user-1",
    userName: "Sarah Miller",
    userAvatar: null,
    userInitials: "SM",
    action: "completed",
    target: "Foundation Inspection",
    details: "All quality checks passed",
    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
    metadata: { status: "approved" }
  },
  {
    id: "5",
    type: "alert",
    userId: "system",
    userName: "System",
    userAvatar: null,
    userInitials: "SY",
    action: "detected",
    target: "Weather Alert",
    details: "Rain expected tomorrow - outdoor work may be affected",
    timestamp: new Date(Date.now() - 1000 * 60 * 20), // 20 minutes ago
    metadata: { severity: "medium", category: "weather" }
  },
  {
    id: "6",
    type: "team_update",
    userId: "user-4",
    userName: "Lisa Chen",
    userAvatar: null,
    userInitials: "LC",
    action: "joined",
    target: "Project Team",
    details: "Added as Electrical Engineer",
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    metadata: { role: "electrical_engineer" }
  }
];

const getActivityIcon = (type: string) => {
  switch (type) {
    case "progress_update":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case "file_upload":
      return <Upload className="h-4 w-4 text-blue-500" />;
    case "comment":
      return <MessageSquare className="h-4 w-4 text-purple-500" />;
    case "milestone":
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case "alert":
      return <AlertTriangle className="h-4 w-4 text-orange-500" />;
    case "team_update":
      return <Users className="h-4 w-4 text-blue-600" />;
    default:
      return <Activity className="h-4 w-4 text-gray-500" />;
  }
};

const getActivityColor = (type: string) => {
  switch (type) {
    case "progress_update":
      return "border-green-200 bg-green-50";
    case "file_upload":
      return "border-blue-200 bg-blue-50";
    case "comment":
      return "border-purple-200 bg-purple-50";
    case "milestone":
      return "border-green-200 bg-green-50";
    case "alert":
      return "border-orange-200 bg-orange-50";
    case "team_update":
      return "border-blue-200 bg-blue-50";
    default:
      return "border-gray-200 bg-gray-50";
  }
};

const formatTimeAgo = (date: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return "Just now";
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays}d ago`;
};

export function ConstructionActivityFeed({ projectId }: ActivityFeedProps) {
  const [activities, setActivities] = useState(mockActivities);
  const [isLive, setIsLive] = useState(true);

  // Simulate real-time updates
  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      // Simulate new activity every 30 seconds
      if (Math.random() > 0.7) {
        const newActivity = {
          id: Date.now().toString(),
          type: "progress_update",
          userId: "user-live",
          userName: "Live Update",
          userAvatar: null,
          userInitials: "LU",
          action: "updated",
          target: "Live Progress",
          details: "Real-time update simulation",
          timestamp: new Date(),
          metadata: { isLive: true }
        };
        
        setActivities(prev => [newActivity, ...prev.slice(0, 9)]); // Keep only 10 items
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isLive]);

  return (
    <Card className="w-80 bg-background/95 backdrop-blur-sm border shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Live Activity</CardTitle>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-xs text-muted-foreground">
              {isLive ? 'Live' : 'Paused'}
            </span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className="h-96 px-4 pb-4">
          <div className="space-y-3">
            {activities.map((activity) => (
              <div 
                key={activity.id} 
                className={`p-3 rounded-lg border ${getActivityColor(activity.type)} transition-all hover:shadow-sm`}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getActivityIcon(activity.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Avatar className="w-5 h-5">
                        <AvatarImage src={activity.userAvatar} />
                        <AvatarFallback className="text-xs bg-blue-500 text-white">
                          {activity.userInitials}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs font-medium">{activity.userName}</span>
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(activity.timestamp)}
                      </span>
                    </div>
                    
                    <div className="text-xs">
                      <span className="text-muted-foreground">{activity.action} </span>
                      <span className="font-medium">{activity.target}</span>
                    </div>
                    
                    {activity.details && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {activity.details}
                      </div>
                    )}
                    
                    {/* Activity-specific metadata */}
                    {activity.type === "progress_update" && activity.metadata.progress && (
                      <div className="mt-2">
                        <div className="flex items-center gap-2 text-xs">
                          <span>Progress:</span>
                          <Badge variant="secondary" className="text-xs">
                            {activity.metadata.progress}%
                          </Badge>
                        </div>
                      </div>
                    )}
                    
                    {activity.type === "file_upload" && (
                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          {activity.metadata.fileCount} files
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
