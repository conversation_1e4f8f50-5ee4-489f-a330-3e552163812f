"use client";

import { useState } from "react";
import { Button } from "@midday/ui/button";
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@midday/ui/sheet";
import { Badge } from "@midday/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@midday/ui/tabs";
import { ConstructionProjectDetails } from "./project-details";
import { ConstructionTeamPresence } from "./team-presence";
import { ConstructionActivityFeed } from "./activity-feed";
import { 
  Menu, 
  Users, 
  Activity, 
  BarChart3, 
  FileText, 
  Settings,
  ArrowLeft,
  Phone,
  MessageSquare
} from "lucide-react";

interface MobileNavProps {
  project: any;
  siteMeasurements: any[];
  teamPresence: any[];
  projectId: string;
  userRole: any;
  permissions: any;
  onBack: () => void;
}

export function ConstructionMobileNav({
  project,
  siteMeasurements,
  teamPresence,
  projectId,
  userRole,
  permissions,
  onBack
}: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("details");

  const completion = project?.completionPercentage || 0;

  return (
    <>
      {/* Mobile Header Bar */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="font-semibold text-sm truncate max-w-32">
                {project?.name || "Construction Project"}
              </h1>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{completion}% complete</span>
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  {project?.status?.replace('_', ' ') || 'Active'}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Quick Actions */}
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MessageSquare className="h-4 w-4" />
            </Button>
            
            {/* Menu Trigger */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              
              <SheetContent side="right" className="w-full sm:w-96 p-0">
                <div className="h-full flex flex-col">
                  <SheetHeader className="p-4 border-b">
                    <SheetTitle className="text-left">Project Details</SheetTitle>
                  </SheetHeader>
                  
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                    <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
                      <TabsTrigger value="details" className="text-xs">
                        <FileText className="h-3 w-3 mr-1" />
                        Details
                      </TabsTrigger>
                      <TabsTrigger value="team" className="text-xs">
                        <Users className="h-3 w-3 mr-1" />
                        Team
                      </TabsTrigger>
                      <TabsTrigger value="activity" className="text-xs">
                        <Activity className="h-3 w-3 mr-1" />
                        Activity
                      </TabsTrigger>
                      <TabsTrigger value="analytics" className="text-xs">
                        <BarChart3 className="h-3 w-3 mr-1" />
                        Analytics
                      </TabsTrigger>
                    </TabsList>
                    
                    <div className="flex-1 overflow-hidden">
                      <TabsContent value="details" className="h-full m-0">
                        <div className="h-full overflow-y-auto">
                          <ConstructionProjectDetails
                            project={project}
                            siteMeasurements={siteMeasurements}
                            onBack={() => setIsOpen(false)}
                            userRole={userRole}
                            permissions={permissions}
                          />
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="team" className="h-full m-0">
                        <div className="h-full overflow-y-auto p-4">
                          <ConstructionTeamPresence
                            projectId={projectId}
                            teamPresence={teamPresence}
                            userRole={userRole}
                          />
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="activity" className="h-full m-0">
                        <div className="h-full overflow-y-auto p-4">
                          <ConstructionActivityFeed
                            projectId={projectId}
                          />
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="analytics" className="h-full m-0">
                        <div className="h-full overflow-y-auto p-4">
                          <div className="space-y-4">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-orange-500 mb-2">
                                {completion}%
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Project Complete
                              </div>
                            </div>
                            
                            {/* Key Metrics */}
                            <div className="grid grid-cols-2 gap-4">
                              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                <div className="text-xs text-muted-foreground">Workers</div>
                                <div className="text-lg font-bold">24</div>
                              </div>
                              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                <div className="text-xs text-muted-foreground">Equipment</div>
                                <div className="text-lg font-bold">8</div>
                              </div>
                              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                <div className="text-xs text-muted-foreground">Cut Volume</div>
                                <div className="text-lg font-bold">250K</div>
                                <div className="text-xs text-muted-foreground">cu yd</div>
                              </div>
                              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                <div className="text-xs text-muted-foreground">Fill Volume</div>
                                <div className="text-lg font-bold">24K</div>
                                <div className="text-xs text-muted-foreground">cu yd</div>
                              </div>
                            </div>
                            
                            {/* Work Areas Progress */}
                            <div className="space-y-3">
                              <h4 className="font-medium">Work Areas</h4>
                              {[
                                { name: "Foundation", progress: 85, color: "bg-orange-500" },
                                { name: "Framing", progress: 60, color: "bg-red-500" },
                                { name: "Electrical", progress: 40, color: "bg-blue-500" },
                                { name: "Plumbing", progress: 25, color: "bg-green-500" }
                              ].map((area) => (
                                <div key={area.name} className="space-y-1">
                                  <div className="flex justify-between text-sm">
                                    <span>{area.name}</span>
                                    <span className="font-medium">{area.progress}%</span>
                                  </div>
                                  <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                                    <div 
                                      className={`h-full ${area.color} rounded-full transition-all`}
                                      style={{ width: `${area.progress}%` }}
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </div>
                  </Tabs>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-t">
        <div className="grid grid-cols-4 gap-1 p-2">
          <Button 
            variant={activeTab === "details" ? "default" : "ghost"} 
            size="sm" 
            className="flex flex-col gap-1 h-auto py-2"
            onClick={() => {
              setActiveTab("details");
              setIsOpen(true);
            }}
          >
            <FileText className="h-4 w-4" />
            <span className="text-xs">Details</span>
          </Button>
          
          <Button 
            variant={activeTab === "team" ? "default" : "ghost"} 
            size="sm" 
            className="flex flex-col gap-1 h-auto py-2"
            onClick={() => {
              setActiveTab("team");
              setIsOpen(true);
            }}
          >
            <Users className="h-4 w-4" />
            <span className="text-xs">Team</span>
          </Button>
          
          <Button 
            variant={activeTab === "activity" ? "default" : "ghost"} 
            size="sm" 
            className="flex flex-col gap-1 h-auto py-2"
            onClick={() => {
              setActiveTab("activity");
              setIsOpen(true);
            }}
          >
            <Activity className="h-4 w-4" />
            <span className="text-xs">Activity</span>
          </Button>
          
          <Button 
            variant={activeTab === "analytics" ? "default" : "ghost"} 
            size="sm" 
            className="flex flex-col gap-1 h-auto py-2"
            onClick={() => {
              setActiveTab("analytics");
              setIsOpen(true);
            }}
          >
            <BarChart3 className="h-4 w-4" />
            <span className="text-xs">Analytics</span>
          </Button>
        </div>
      </div>
    </>
  );
}
