"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@midday/ui/avatar";
import { Badge } from "@midday/ui/badge";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { Card, CardContent } from "@midday/ui/card";
import { Input } from "@midday/ui/input";
import { ScrollArea } from "@midday/ui/scroll-area";
import { Plus, MessageSquare, Send, Phone, Video } from "lucide-react";
import { useState, useEffect } from "react";

interface TeamPresenceProps {
  projectId: string;
  teamPresence: any[];
  userRole?: any;
}

// Mock team data - in real app this would come from teamPresence prop
const mockTeamMembers = [
  {
    id: "1",
    name: "<PERSON>",
    role: "Project Manager",
    avatar: null,
    isOnline: true,
    lastSeen: new Date(),
    initials: "JD",
    currentActivity: "Reviewing blueprints",
    location: "Site Office"
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "Site Engineer",
    avatar: null,
    isOnline: true,
    lastSeen: new Date(),
    initials: "<PERSON>",
    currentActivity: "Foundation inspection",
    location: "Building A"
  },
  {
    id: "3",
    name: "<PERSON>",
    role: "Contractor",
    avatar: null,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
    initials: "MJ",
    currentActivity: "Off-site",
    location: "Equipment yard"
  }
];

// Mock chat messages
const mockMessages = [
  {
    id: "1",
    userId: "1",
    userName: "John Doe",
    message: "Foundation inspection completed. Looking good!",
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    type: "message"
  },
  {
    id: "2",
    userId: "2",
    userName: "Sarah Miller",
    message: "Great! Moving to electrical phase next week.",
    timestamp: new Date(Date.now() - 1000 * 60 * 3),
    type: "message"
  },
  {
    id: "3",
    userId: "1",
    userName: "John Doe",
    message: "Equipment delivery scheduled for tomorrow 8 AM",
    timestamp: new Date(Date.now() - 1000 * 60 * 1),
    type: "announcement"
  }
];

export function ConstructionTeamPresence({ projectId, teamPresence, userRole }: TeamPresenceProps) {
  const [showChat, setShowChat] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [messages, setMessages] = useState(mockMessages);

  const activeMembers = mockTeamMembers.filter(member => member.isOnline);
  const totalMembers = mockTeamMembers.length;

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now().toString(),
        userId: "current-user",
        userName: "You",
        message: newMessage,
        timestamp: new Date(),
        type: "message" as const
      };
      setMessages([...messages, message]);
      setNewMessage("");
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card className={`${showChat ? 'w-80' : 'w-64'} bg-background/95 backdrop-blur-sm border shadow-lg transition-all duration-300`}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {activeMembers.slice(0, 3).map((member) => (
                  <div key={member.id} className="relative">
                    <Avatar className="w-8 h-8 border-2 border-white">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs bg-blue-500 text-white">
                        {member.initials}
                      </AvatarFallback>
                    </Avatar>
                    {member.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                    )}
                  </div>
                ))}
                {totalMembers > 3 && (
                  <div className="w-8 h-8 bg-gray-200 border-2 border-white rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      +{totalMembers - 3}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setShowChat(!showChat)}
              >
                <MessageSquare className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Team Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Team Online</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {activeMembers.length} of {totalMembers}
              </Badge>
            </div>

            {/* Individual Members */}
            <div className="space-y-2">
              {mockTeamMembers.map((member) => (
                <div key={member.id} className="flex items-start gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <div className="relative">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs bg-blue-500 text-white">
                        {member.initials}
                      </AvatarFallback>
                    </Avatar>
                    <div
                      className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 border-2 border-white rounded-full ${
                        member.isOnline ? 'bg-green-500' : 'bg-gray-400'
                      }`}
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <div className="text-sm font-medium truncate">
                        {member.name}
                      </div>
                      {member.isOnline && (
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                            <Phone className="h-3 w-3" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                            <Video className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {member.role}
                    </div>
                    {member.isOnline && (
                      <div className="text-xs text-blue-600 truncate">
                        {member.currentActivity}
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground truncate">
                      📍 {member.location}
                    </div>
                  </div>

                  <div className="text-xs text-muted-foreground text-right">
                    {member.isOnline ? (
                      <span className="text-green-600 font-medium">Online</span>
                    ) : (
                      <span>15m ago</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Chat Interface */}
          {showChat && (
            <div className="pt-4 border-t space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Team Chat</h4>
                <Badge variant="secondary" className="text-xs">
                  {messages.length} messages
                </Badge>
              </div>

              {/* Messages */}
              <ScrollArea className="h-32 w-full">
                <div className="space-y-2 pr-2">
                  {messages.map((message) => (
                    <div key={message.id} className="text-xs">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{message.userName}</span>
                        <span className="text-muted-foreground">
                          {formatTime(message.timestamp)}
                        </span>
                        {message.type === "announcement" && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            📢
                          </Badge>
                        )}
                      </div>
                      <div className="text-muted-foreground pl-2 border-l-2 border-gray-200">
                        {message.message}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {/* Message Input */}
              <div className="flex gap-2">
                <Input
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  className="text-xs h-8"
                />
                <Button
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                >
                  <Send className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="pt-2 border-t space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start text-xs">
              <Plus className="h-3 w-3 mr-2" />
              Invite Team Member
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
