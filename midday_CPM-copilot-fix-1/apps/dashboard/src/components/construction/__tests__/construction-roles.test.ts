import { describe, it, expect } from 'vitest';
import {
  ConstructionRole,
  Permission,
  hasPermission,
  canUserAccess,
  getUserPermissions,
  getContextualPermissions,
  shouldShowFeature,
  getFilteredMenuItems,
  ROLE_PERMISSIONS,
  ROLE_INFO
} from '@/lib/construction-roles';

describe('Construction Roles System', () => {
  describe('hasPermission', () => {
    it('should return true for project manager with edit_project permission', () => {
      expect(hasPermission('project_manager', 'edit_project')).toBe(true);
    });

    it('should return false for worker with edit_project permission', () => {
      expect(hasPermission('worker', 'edit_project')).toBe(false);
    });

    it('should return true for client with view_project permission', () => {
      expect(hasPermission('client', 'view_project')).toBe(true);
    });

    it('should return false for invalid role', () => {
      expect(hasPermission('invalid_role' as ConstructionRole, 'view_project')).toBe(false);
    });
  });

  describe('canUserAccess', () => {
    it('should return true when user has all required permissions', () => {
      const permissions: Permission[] = ['view_project', 'add_comments'];
      expect(canUserAccess('site_engineer', permissions)).toBe(true);
    });

    it('should return false when user lacks some permissions', () => {
      const permissions: Permission[] = ['view_project', 'delete_project'];
      expect(canUserAccess('worker', permissions)).toBe(false);
    });

    it('should return true for empty permissions array', () => {
      expect(canUserAccess('worker', [])).toBe(true);
    });
  });

  describe('getUserPermissions', () => {
    it('should return correct permissions for project manager', () => {
      const permissions = getUserPermissions('project_manager');
      expect(permissions).toContain('view_project');
      expect(permissions).toContain('edit_project');
      expect(permissions).toContain('manage_team');
      expect(permissions).toContain('view_financials');
    });

    it('should return limited permissions for worker', () => {
      const permissions = getUserPermissions('worker');
      expect(permissions).toContain('view_project');
      expect(permissions).toContain('add_comments');
      expect(permissions).not.toContain('edit_project');
      expect(permissions).not.toContain('manage_team');
    });

    it('should return empty array for invalid role', () => {
      const permissions = getUserPermissions('invalid_role' as ConstructionRole);
      expect(permissions).toEqual([]);
    });
  });

  describe('getContextualPermissions', () => {
    it('should add project owner permissions', () => {
      const permissions = getContextualPermissions('contractor', {
        isProjectOwner: true
      });
      expect(permissions).toContain('delete_project');
      expect(permissions).toContain('manage_team');
    });

    it('should add team member permissions', () => {
      const permissions = getContextualPermissions('worker', {
        isTeamMember: true
      });
      expect(permissions).toContain('add_comments');
      expect(permissions).toContain('view_project');
    });

    it('should add phase-specific permissions for architect in planning', () => {
      const permissions = getContextualPermissions('architect', {
        projectPhase: 'planning'
      });
      expect(permissions).toContain('edit_project');
    });

    it('should remove duplicates', () => {
      const permissions = getContextualPermissions('project_manager', {
        isProjectOwner: true,
        isTeamMember: true
      });
      const uniquePermissions = [...new Set(permissions)];
      expect(permissions.length).toBe(uniquePermissions.length);
    });
  });

  describe('shouldShowFeature', () => {
    it('should show financial dashboard for project manager', () => {
      expect(shouldShowFeature('project_manager', 'financial_dashboard')).toBe(true);
    });

    it('should not show financial dashboard for worker', () => {
      expect(shouldShowFeature('worker', 'financial_dashboard')).toBe(false);
    });

    it('should show team management for project manager', () => {
      expect(shouldShowFeature('project_manager', 'team_management')).toBe(true);
    });

    it('should show unknown features by default', () => {
      expect(shouldShowFeature('worker', 'unknown_feature')).toBe(true);
    });
  });

  describe('getFilteredMenuItems', () => {
    it('should return all menu items for project manager', () => {
      const menuItems = getFilteredMenuItems('project_manager');
      expect(menuItems.length).toBeGreaterThan(5);
      expect(menuItems.find(item => item.id === 'financials')).toBeDefined();
      expect(menuItems.find(item => item.id === 'settings')).toBeDefined();
    });

    it('should return limited menu items for client', () => {
      const menuItems = getFilteredMenuItems('client');
      expect(menuItems.find(item => item.id === 'financials')).toBeDefined();
      expect(menuItems.find(item => item.id === 'settings')).toBeUndefined();
    });

    it('should return basic menu items for worker', () => {
      const menuItems = getFilteredMenuItems('worker');
      expect(menuItems.find(item => item.id === 'overview')).toBeDefined();
      expect(menuItems.find(item => item.id === 'progress')).toBeDefined();
      expect(menuItems.find(item => item.id === 'financials')).toBeUndefined();
    });
  });

  describe('ROLE_PERMISSIONS', () => {
    it('should have permissions defined for all roles', () => {
      const roles: ConstructionRole[] = [
        'project_manager', 'site_engineer', 'architect', 'contractor',
        'subcontractor', 'client', 'inspector', 'safety_officer', 'foreman', 'worker'
      ];

      roles.forEach(role => {
        expect(ROLE_PERMISSIONS[role]).toBeDefined();
        expect(Array.isArray(ROLE_PERMISSIONS[role])).toBe(true);
      });
    });

    it('should ensure project manager has the most permissions', () => {
      const managerPermissions = ROLE_PERMISSIONS.project_manager;
      const otherRoles: ConstructionRole[] = [
        'site_engineer', 'architect', 'contractor', 'client', 'worker'
      ];

      otherRoles.forEach(role => {
        expect(managerPermissions.length).toBeGreaterThanOrEqual(
          ROLE_PERMISSIONS[role].length
        );
      });
    });

    it('should ensure all roles have view_project permission', () => {
      Object.keys(ROLE_PERMISSIONS).forEach(role => {
        const permissions = ROLE_PERMISSIONS[role as ConstructionRole];
        expect(permissions).toContain('view_project');
      });
    });
  });

  describe('ROLE_INFO', () => {
    it('should have info defined for all roles', () => {
      const roles: ConstructionRole[] = [
        'project_manager', 'site_engineer', 'architect', 'contractor',
        'subcontractor', 'client', 'inspector', 'safety_officer', 'foreman', 'worker'
      ];

      roles.forEach(role => {
        const info = ROLE_INFO[role];
        expect(info).toBeDefined();
        expect(info.label).toBeDefined();
        expect(info.description).toBeDefined();
        expect(info.color).toBeDefined();
        expect(info.icon).toBeDefined();
      });
    });

    it('should have unique labels for all roles', () => {
      const labels = Object.values(ROLE_INFO).map(info => info.label);
      const uniqueLabels = [...new Set(labels)];
      expect(labels.length).toBe(uniqueLabels.length);
    });
  });

  describe('Permission hierarchy', () => {
    it('should ensure managers can do everything engineers can do', () => {
      const managerPermissions = ROLE_PERMISSIONS.project_manager;
      const engineerPermissions = ROLE_PERMISSIONS.site_engineer;

      engineerPermissions.forEach(permission => {
        expect(managerPermissions).toContain(permission);
      });
    });

    it('should ensure contractors have basic project permissions', () => {
      const contractorPermissions = ROLE_PERMISSIONS.contractor;
      expect(contractorPermissions).toContain('view_project');
      expect(contractorPermissions).toContain('upload_files');
      expect(contractorPermissions).toContain('add_comments');
      expect(contractorPermissions).toContain('update_progress');
    });

    it('should ensure clients have limited but appropriate permissions', () => {
      const clientPermissions = ROLE_PERMISSIONS.client;
      expect(clientPermissions).toContain('view_project');
      expect(clientPermissions).toContain('view_financials');
      expect(clientPermissions).not.toContain('edit_project');
      expect(clientPermissions).not.toContain('manage_team');
    });
  });

  describe('Security constraints', () => {
    it('should not allow workers to manage teams', () => {
      expect(hasPermission('worker', 'manage_team')).toBe(false);
    });

    it('should not allow subcontractors to view financials', () => {
      expect(hasPermission('subcontractor', 'view_financials')).toBe(false);
    });

    it('should not allow clients to delete projects', () => {
      expect(hasPermission('client', 'delete_project')).toBe(false);
    });

    it('should ensure only authorized roles can approve milestones', () => {
      const authorizedRoles = ['project_manager'];
      const unauthorizedRoles = ['worker', 'subcontractor', 'client'];

      authorizedRoles.forEach(role => {
        expect(hasPermission(role as ConstructionRole, 'approve_milestones')).toBe(true);
      });

      unauthorizedRoles.forEach(role => {
        expect(hasPermission(role as ConstructionRole, 'approve_milestones')).toBe(false);
      });
    });
  });
});
