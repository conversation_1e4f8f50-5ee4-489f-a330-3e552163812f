import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  ContractorAccess,
  ClientRestricted,
  CanUploadFiles,
  CanEditProject,
  CanViewFinancials,
  CanManageTeam
} from '@/components/construction/role-guard';
import { ConstructionRole } from '@/lib/construction-roles';

// Mock the permissions hook
vi.mock('@/hooks/use-construction-permissions', () => ({
  useConstructionPermissions: ({ userRole }: { userRole: ConstructionRole }) => ({
    checkMultiplePermissions: (permissions: string[]) => {
      // Mock permission logic based on role
      const rolePermissions: Record<ConstructionRole, string[]> = {
        project_manager: ['view_project', 'edit_project', 'manage_team', 'upload_files', 'view_financials'],
        site_engineer: ['view_project', 'edit_project', 'upload_files'],
        architect: ['view_project', 'upload_files'],
        contractor: ['view_project', 'upload_files'],
        subcontractor: ['view_project'],
        client: ['view_project', 'view_financials'],
        inspector: ['view_project', 'upload_files'],
        safety_officer: ['view_project', 'upload_files'],
        foreman: ['view_project', 'upload_files'],
        worker: ['view_project']
      };
      
      const userPermissions = rolePermissions[userRole] || [];
      return permissions.every(p => userPermissions.includes(p));
    },
    canShowFeature: (feature: string) => {
      const featureAccess: Record<string, ConstructionRole[]> = {
        financial_dashboard: ['project_manager', 'client'],
        team_management: ['project_manager'],
        project_settings: ['project_manager', 'site_engineer']
      };
      
      return featureAccess[feature]?.includes(userRole) ?? true;
    }
  })
}));

describe('RoleGuard Component', () => {
  const TestContent = () => <div>Protected Content</div>;
  const FallbackContent = () => <div>Access Denied</div>;

  describe('Basic RoleGuard', () => {
    it('should render content for authorized role', () => {
      render(
        <RoleGuard userRole="project_manager" requiredRole="project_manager">
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for unauthorized role', () => {
      render(
        <RoleGuard 
          userRole="worker" 
          requiredRole="project_manager"
          fallback={<FallbackContent />}
        >
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('should render content for multiple allowed roles', () => {
      render(
        <RoleGuard 
          userRole="site_engineer" 
          requiredRole={['project_manager', 'site_engineer']}
        >
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render default fallback when no custom fallback provided', () => {
      render(
        <RoleGuard userRole="worker" requiredRole="project_manager">
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText(/Access restricted to/)).toBeInTheDocument();
    });

    it('should not render anything when showFallback is false', () => {
      const { container } = render(
        <RoleGuard 
          userRole="worker" 
          requiredRole="project_manager"
          showFallback={false}
        >
          <TestContent />
        </RoleGuard>
      );
      
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Permission-based access', () => {
    it('should render content when user has required permissions', () => {
      render(
        <RoleGuard 
          userRole="project_manager" 
          requiredPermissions={['view_project', 'edit_project']}
        >
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback when user lacks permissions', () => {
      render(
        <RoleGuard 
          userRole="worker" 
          requiredPermissions={['edit_project', 'manage_team']}
          fallback={<FallbackContent />}
        >
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('Feature-based access', () => {
    it('should render content for authorized feature access', () => {
      render(
        <RoleGuard userRole="project_manager" feature="financial_dashboard">
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for unauthorized feature access', () => {
      render(
        <RoleGuard 
          userRole="worker" 
          feature="financial_dashboard"
          fallback={<FallbackContent />}
        >
          <TestContent />
        </RoleGuard>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });
});

describe('Convenience Components', () => {
  const TestContent = () => <div>Protected Content</div>;
  const FallbackContent = () => <div>Access Denied</div>;

  describe('ManagerOnly', () => {
    it('should render content for project manager', () => {
      render(
        <ManagerOnly userRole="project_manager">
          <TestContent />
        </ManagerOnly>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for non-manager', () => {
      render(
        <ManagerOnly userRole="worker" fallback={<FallbackContent />}>
          <TestContent />
        </ManagerOnly>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('EngineerOrManager', () => {
    it('should render content for project manager', () => {
      render(
        <EngineerOrManager userRole="project_manager">
          <TestContent />
        </EngineerOrManager>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render content for site engineer', () => {
      render(
        <EngineerOrManager userRole="site_engineer">
          <TestContent />
        </EngineerOrManager>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for worker', () => {
      render(
        <EngineerOrManager userRole="worker" fallback={<FallbackContent />}>
          <TestContent />
        </EngineerOrManager>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('ContractorAccess', () => {
    it('should render content for contractor', () => {
      render(
        <ContractorAccess userRole="contractor">
          <TestContent />
        </ContractorAccess>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render content for subcontractor', () => {
      render(
        <ContractorAccess userRole="subcontractor">
          <TestContent />
        </ContractorAccess>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render content for foreman', () => {
      render(
        <ContractorAccess userRole="foreman">
          <TestContent />
        </ContractorAccess>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for client', () => {
      render(
        <ContractorAccess userRole="client" fallback={<FallbackContent />}>
          <TestContent />
        </ContractorAccess>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('ClientRestricted', () => {
    it('should render content for project manager', () => {
      render(
        <ClientRestricted userRole="project_manager">
          <TestContent />
        </ClientRestricted>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render content for contractor', () => {
      render(
        <ClientRestricted userRole="contractor">
          <TestContent />
        </ClientRestricted>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for client', () => {
      render(
        <ClientRestricted userRole="client" fallback={<FallbackContent />}>
          <TestContent />
        </ClientRestricted>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });
});

describe('Permission-based Guards', () => {
  const TestContent = () => <div>Protected Content</div>;
  const FallbackContent = () => <div>Access Denied</div>;

  describe('CanUploadFiles', () => {
    it('should render content for users with upload permission', () => {
      render(
        <CanUploadFiles userRole="project_manager">
          <TestContent />
        </CanUploadFiles>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for users without upload permission', () => {
      render(
        <CanUploadFiles userRole="worker" fallback={<FallbackContent />}>
          <TestContent />
        </CanUploadFiles>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('CanEditProject', () => {
    it('should render content for users with edit permission', () => {
      render(
        <CanEditProject userRole="project_manager">
          <TestContent />
        </CanEditProject>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for users without edit permission', () => {
      render(
        <CanEditProject userRole="worker" fallback={<FallbackContent />}>
          <TestContent />
        </CanEditProject>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('CanViewFinancials', () => {
    it('should render content for users with financial access', () => {
      render(
        <CanViewFinancials userRole="project_manager">
          <TestContent />
        </CanViewFinancials>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render content for clients', () => {
      render(
        <CanViewFinancials userRole="client">
          <TestContent />
        </CanViewFinancials>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for users without financial access', () => {
      render(
        <CanViewFinancials userRole="worker" fallback={<FallbackContent />}>
          <TestContent />
        </CanViewFinancials>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('CanManageTeam', () => {
    it('should render content for users with team management permission', () => {
      render(
        <CanManageTeam userRole="project_manager">
          <TestContent />
        </CanManageTeam>
      );
      
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should render fallback for users without team management permission', () => {
      render(
        <CanManageTeam userRole="contractor" fallback={<FallbackContent />}>
          <TestContent />
        </CanManageTeam>
      );
      
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });
});
