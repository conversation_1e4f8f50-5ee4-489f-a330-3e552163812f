"use client";

import { ReactNode } from "react";
import { ConstructionRole, Permission } from "@/lib/construction-roles";
import { useConstructionPermissions } from "@/hooks/use-construction-permissions";
import { Alert, AlertDescription } from "@midday/ui/alert";
import { Lock } from "lucide-react";

interface RoleGuardProps {
  children: ReactNode;
  userRole: ConstructionRole;
  requiredPermissions?: Permission[];
  requiredRole?: ConstructionRole | ConstructionRole[];
  feature?: string;
  fallback?: ReactNode;
  showFallback?: boolean;
  projectContext?: {
    isProjectOwner?: boolean;
    isTeamMember?: boolean;
    projectPhase?: string;
    projectId?: string;
  };
}

export function RoleGuard({
  children,
  userRole,
  requiredPermissions = [],
  requiredRole,
  feature,
  fallback,
  showFallback = true,
  projectContext
}: RoleGuardProps) {
  const { 
    checkMultiplePermissions, 
    canShowFeature 
  } = useConstructionPermissions({ 
    userRole, 
    projectContext 
  });

  // Check role-based access
  if (requiredRole) {
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    if (!allowedRoles.includes(userRole)) {
      return showFallback ? (
        fallback || (
          <Alert className="border-orange-200 bg-orange-50">
            <Lock className="h-4 w-4" />
            <AlertDescription>
              Access restricted to: {allowedRoles.join(", ").replace(/_/g, " ")}
            </AlertDescription>
          </Alert>
        )
      ) : null;
    }
  }

  // Check permission-based access
  if (requiredPermissions.length > 0) {
    if (!checkMultiplePermissions(requiredPermissions)) {
      return showFallback ? (
        fallback || (
          <Alert className="border-red-200 bg-red-50">
            <Lock className="h-4 w-4" />
            <AlertDescription>
              Insufficient permissions to view this content.
            </AlertDescription>
          </Alert>
        )
      ) : null;
    }
  }

  // Check feature-based access
  if (feature) {
    if (!canShowFeature(feature)) {
      return showFallback ? (
        fallback || (
          <Alert className="border-gray-200 bg-gray-50">
            <Lock className="h-4 w-4" />
            <AlertDescription>
              This feature is not available for your role.
            </AlertDescription>
          </Alert>
        )
      ) : null;
    }
  }

  return <>{children}</>;
}

// Convenience components for common role checks
export function ManagerOnly({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredRole="project_manager"
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function EngineerOrManager({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredRole={["project_manager", "site_engineer"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function ContractorAccess({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredRole={["contractor", "subcontractor", "foreman"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function ClientRestricted({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredRole={["project_manager", "site_engineer", "architect", "contractor"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

// Permission-based guards
export function CanUploadFiles({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredPermissions={["upload_files"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function CanEditProject({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredPermissions={["edit_project"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function CanViewFinancials({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredPermissions={["view_financials"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function CanManageTeam({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      requiredPermissions={["manage_team"]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

// Feature-based guards
export function FinancialDashboard({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      feature="financial_dashboard"
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function TeamManagement({ children, userRole, fallback }: {
  children: ReactNode;
  userRole: ConstructionRole;
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      userRole={userRole}
      feature="team_management"
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

// Hook for conditional rendering
export function useRoleBasedRendering(userRole: ConstructionRole) {
  const permissions = useConstructionPermissions({ userRole });
  
  return {
    renderIf: (condition: keyof typeof permissions.permissions | keyof typeof permissions.features) => {
      const value = permissions.permissions[condition as keyof typeof permissions.permissions] || 
                   permissions.features[condition as keyof typeof permissions.features];
      return (component: ReactNode) => value ? component : null;
    },
    
    renderForRole: (allowedRoles: ConstructionRole | ConstructionRole[]) => {
      const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
      return (component: ReactNode) => roles.includes(userRole) ? component : null;
    },
    
    renderWithFallback: (
      condition: keyof typeof permissions.permissions | keyof typeof permissions.features,
      component: ReactNode,
      fallback: ReactNode
    ) => {
      const value = permissions.permissions[condition as keyof typeof permissions.permissions] || 
                   permissions.features[condition as keyof typeof permissions.features];
      return value ? component : fallback;
    }
  };
}
