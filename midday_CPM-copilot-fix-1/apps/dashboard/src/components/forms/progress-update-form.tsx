"use client";

import { FileUpload, type UploadedFile } from "@/components/file-upload";
import { useZodForm } from "@/hooks/use-zod-form";
import { useTRPC } from "@/trpc/client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@midday/ui/form";
import { Input } from "@midday/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@midday/ui/select";
import { SubmitButton } from "@midday/ui/submit-button";
import { Textarea } from "@midday/ui/textarea";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { z } from "zod";

const formSchema = z.object({
  projectId: z.string().uuid(),
  phase: z
    .enum([
      "site_preparation",
      "foundation",
      "framing",
      "roofing",
      "electrical",
      "plumbing",
      "insulation",
      "drywall",
      "flooring",
      "painting",
      "final_inspection",
    ])
    .optional(),
  progressPercentage: z.number().min(0).max(100),
  description: z.string().optional(),
  notes: z.string().optional(),
  workCompleted: z.string().optional(),
  nextSteps: z.string().optional(),
  weatherConditions: z.string().optional(),
  workersOnSite: z.number().optional(),
  issuesReported: z.string().optional(),
  photos: z.array(z.string()).optional(),
  equipmentUsed: z.object({
    excavators: z.number().optional(),
    cranes: z.number().optional(),
    trucks: z.number().optional(),
    other: z.string().optional(),
  }).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface Props {
  projectId: string;
  onSuccess?: () => void;
}

const phaseLabels = {
  site_preparation: "Site Preparation",
  foundation: "Foundation",
  framing: "Framing",
  roofing: "Roofing",
  electrical: "Electrical",
  plumbing: "Plumbing",
  insulation: "Insulation",
  drywall: "Drywall",
  flooring: "Flooring",
  painting: "Painting",
  final_inspection: "Final Inspection",
};

export function ProgressUpdateForm({ projectId, onSuccess }: Props) {
  const api = useTRPC();
  const queryClient = useQueryClient();
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedFile[]>([]);

  const form = useZodForm({
    schema: formSchema,
    defaultValues: {
      projectId,
      progressPercentage: 0,
      description: "",
      notes: "",
      workCompleted: "",
      nextSteps: "",
      weatherConditions: "",
      workersOnSite: undefined,
      issuesReported: "",
      photos: [],
      equipmentUsed: {
        excavators: undefined,
        cranes: undefined,
        trucks: undefined,
        other: "",
      },
    },
  });

  const mutation = useMutation({
    mutationFn: (values: FormValues) => {
      // Include photo URLs in the submission
      const submitData = {
        ...values,
        photos: uploadedPhotos.map(photo => photo.url).filter(Boolean) as string[],
      };
      return api.constructionProjects.createProgressUpdate.mutate(submitData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["constructionProjects"],
      });
      form.reset();
      setUploadedPhotos([]);
      onSuccess?.();
    },
  });

  const handlePhotoUpload = async (files: File[]): Promise<UploadedFile[]> => {
    // This would integrate with your file upload service
    // For now, return mock uploaded files
    return files.map((file, index) => ({
      id: `photo-${Date.now()}-${index}`,
      name: file.name,
      size: file.size,
      type: file.type,
      url: `/photos/${file.name}`, // This would be the actual uploaded URL
    }));
  };

  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="phase"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Construction Phase</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select phase" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(phaseLabels).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="progressPercentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Progress Percentage</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    max="100"
                    placeholder="75"
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Work Description</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Describe the work completed..."
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="workCompleted"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Specific Work Completed</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="List specific tasks and milestones completed..."
                  rows={2}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="nextSteps"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Next Steps</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Outline planned next steps..."
                  rows={2}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="weatherConditions"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Weather Conditions</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Sunny, 75°F, light breeze"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="workersOnSite"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Workers on Site</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    placeholder="12"
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="issuesReported"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Issues or Concerns</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Report any issues, delays, or safety concerns..."
                  rows={2}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Notes</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Any additional observations or comments..."
                  rows={2}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Photo Upload Section */}
        <FormField
          control={form.control}
          name="photos"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Progress Photos</FormLabel>
              <FormControl>
                <FileUpload
                  files={uploadedPhotos}
                  onFilesChange={setUploadedPhotos}
                  onUpload={handlePhotoUpload}
                  accept={{
                    "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"]
                  }}
                  maxFiles={10}
                  maxSize={5 * 1024 * 1024} // 5MB
                  description="Upload progress photos to document work completion"
                  multiple
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Equipment Used Section */}
        <FormItem>
          <FormLabel>Equipment Used</FormLabel>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="equipmentUsed.excavators"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Excavators</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min="0"
                      placeholder="0"
                      onChange={(e) => field.onChange(Number(e.target.value) || undefined)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="equipmentUsed.cranes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Cranes</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min="0"
                      placeholder="0"
                      onChange={(e) => field.onChange(Number(e.target.value) || undefined)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="equipmentUsed.trucks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Trucks</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min="0"
                      placeholder="0"
                      onChange={(e) => field.onChange(Number(e.target.value) || undefined)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="equipmentUsed.other"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Other Equipment</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Specify..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </FormItem>

        <SubmitButton
          loading={mutation.isPending}
          disabled={!form.formState.isValid || mutation.isPending}
          className="w-full"
        >
          Submit Progress Update
        </SubmitButton>

        {/* Error Display */}
        {mutation.error && (
          <div className="mt-4 p-3 rounded-md bg-destructive/10 border border-destructive/20">
            <p className="text-sm text-destructive">
              {mutation.error.message || "An error occurred while submitting the progress update"}
            </p>
          </div>
        )}
      </form>
    </Form>
  );
}