"use client";

import { <PERSON><PERSON>, use<PERSON><PERSON>e, useThree } from "@react-three/fiber";
import { 
  OrbitControls, 
  Grid, 
  Environment, 
  Sky, 
  Stats,
  Html,
  Text,
  Box as DreiBox,
  Plane,
  useHelper,
  PerspectiveCamera,
  ContactShadows
} from "@react-three/drei";
import { EffectComposer, Outline, Selection, Select } from "@react-three/postprocessing";
import * as THREE from "three";
import { useState, useRef, useEffect, Suspense } from "react";
import { Button } from "@midday/ui/button";
import { Badge } from "@midday/ui/badge";
import { MessageSquare, Loader2 } from "lucide-react";
import { ConstructionMeasurementTool } from "./construction-measurement-tool";
import { ConstructionTeamPresence } from "./construction-team-presence";
import type { RouterOutputs } from "@api/trpc/routers/_app";

type ConstructionProject = RouterOutputs["constructionProjects"]["getById"];
type Annotation = NonNullable<ConstructionProject>["annotations"][0];

interface SceneProps {
  project: ConstructionProject;
  onAnnotationClick?: (annotation: Annotation) => void;
  layers: { id: string; visible: boolean }[];
  selectedTool: string;
  environmentPreset: string;
  currentUserId?: string;
}

// Terrain component with realistic height mapping
function TerrainMesh({ project }: { project: ConstructionProject }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [geometry] = useState(() => new THREE.PlaneGeometry(100, 100, 64, 64));
  
  useEffect(() => {
    if (meshRef.current) {
      const positions = geometry.attributes.position;
      
      // Generate realistic terrain based on project coordinates
      for (let i = 0; i < positions.count; i++) {
        const x = positions.getX(i);
        const y = positions.getY(i);
        
        // Create varied terrain with multiple noise layers
        const height1 = Math.sin(x * 0.05) * Math.cos(y * 0.05) * 3;
        const height2 = Math.sin(x * 0.1) * Math.cos(y * 0.1) * 1.5;
        const height3 = Math.sin(x * 0.2) * Math.cos(y * 0.2) * 0.5;
        
        const finalHeight = height1 + height2 + height3;
        positions.setZ(i, finalHeight);
      }
      
      positions.needsUpdate = true;
      geometry.computeVertexNormals();
    }
  }, [geometry]);

  return (
    <mesh 
      ref={meshRef} 
      geometry={geometry} 
      rotation={[-Math.PI / 2, 0, 0]} 
      position={[0, -2, 0]}
      receiveShadow
    >
      <meshStandardMaterial 
        color="#8B7355" 
        roughness={0.8}
        metalness={0.1}
      />
    </mesh>
  );
}

// Animated construction building based on completion
function ConstructionBuilding({ project }: { project: ConstructionProject }) {
  const buildingRef = useRef<THREE.Group>(null);
  const [targetScale, setTargetScale] = useState(1);
  
  useEffect(() => {
    if (project?.completionPercentage) {
      setTargetScale(Math.max(0.1, Number(project.completionPercentage) / 100));
    }
  }, [project?.completionPercentage]);
  
  useFrame((state, delta) => {
    if (buildingRef.current) {
      // Smooth animation to target scale
      buildingRef.current.scale.y = THREE.MathUtils.lerp(
        buildingRef.current.scale.y,
        targetScale,
        delta * 2
      );
    }
  });

  return (
    <group ref={buildingRef} castShadow receiveShadow>
      {/* Foundation */}
      <DreiBox args={[20, 1, 15]} position={[0, -1.5, 0]} castShadow>
        <meshStandardMaterial color="#666666" roughness={0.7} />
      </DreiBox>
      
      {/* Main structure with construction phases */}
      <DreiBox args={[18, 8, 13]} position={[0, 3, 0]} castShadow>
        <meshStandardMaterial 
          color={targetScale > 0.8 ? "#E5E7EB" : "#D1D5DB"} 
          roughness={0.6}
        />
      </DreiBox>
      
      {/* Roof (only visible when near completion) */}
      {targetScale > 0.9 && (
        <DreiBox args={[20, 0.5, 15]} position={[0, 7.5, 0]} castShadow>
          <meshStandardMaterial color="#8B4513" roughness={0.8} />
        </DreiBox>
      )}
      
      {/* Construction equipment */}
      {targetScale < 0.8 && (
        <group position={[25, 0, 10]}>
          {/* Crane */}
          <DreiBox args={[2, 20, 2]} position={[0, 10, 0]} castShadow>
            <meshStandardMaterial color="#FFD700" />
          </DreiBox>
          <DreiBox args={[15, 1, 1]} position={[7.5, 18, 0]} castShadow>
            <meshStandardMaterial color="#FFD700" />
          </DreiBox>
        </group>
      )}
    </group>
  );
}

// Interactive annotation markers
function AnnotationMarkers({ 
  annotations, 
  onAnnotationClick,
  visible 
}: { 
  annotations: Annotation[], 
  onAnnotationClick?: (annotation: Annotation) => void,
  visible: boolean
}) {
  if (!visible || !annotations?.length) return null;

  return (
    <group>
      {annotations.map((annotation, index) => {
        // Position annotations around the building
        const angle = (index / annotations.length) * Math.PI * 2;
        const radius = 15;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        
        return (
          <group key={annotation.id} position={[x, 2, z]}>
            {/* 3D marker pole */}
            <DreiBox args={[0.3, 4, 0.3]} position={[0, 2, 0]} castShadow>
              <meshStandardMaterial color="#EF4444" />
            </DreiBox>
            
            {/* Interactive HTML overlay */}
            <Html position={[0, 4.5, 0]} center>
              <div className="relative">
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-red-500 hover:bg-red-600 text-white shadow-lg"
                  onClick={() => onAnnotationClick?.(annotation)}
                >
                  <MessageSquare className="h-3 w-3 mr-1" />
                  {annotation.type}
                </Button>
                
                {/* Tooltip */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-black text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity whitespace-nowrap">
                  {annotation.content}
                </div>
              </div>
            </Html>
          </group>
        );
      })}
    </group>
  );
}

// Camera controller with smooth transitions
function CameraController() {
  const { camera } = useThree();
  
  useEffect(() => {
    // Set initial camera position for optimal view
    camera.position.set(40, 25, 40);
    camera.lookAt(0, 0, 0);
  }, [camera]);

  return (
    <OrbitControls 
      enablePan 
      enableZoom 
      enableRotate 
      maxPolarAngle={Math.PI / 2.2}
      minDistance={10}
      maxDistance={200}
      autoRotate={false}
      autoRotateSpeed={0.5}
    />
  );
}

// Loading fallback
function SceneLoader() {
  return (
    <Html center>
      <div className="flex items-center space-x-2 text-white">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span>Loading 3D scene...</span>
      </div>
    </Html>
  );
}

// Main 3D scene component
export function Construction3DScene({
  project,
  onAnnotationClick,
  layers,
  selectedTool,
  environmentPreset,
  currentUserId = "current-user"
}: SceneProps) {
  const [showStats, setShowStats] = useState(false);

  return (
    <div className="w-full h-full relative">
      <Canvas
        shadows
        camera={{ position: [40, 25, 40], fov: 60 }}
        gl={{ antialias: true, alpha: false }}
        onCreated={({ gl }) => {
          gl.setClearColor('#87CEEB'); // Sky blue background
          gl.shadowMap.enabled = true;
          gl.shadowMap.type = THREE.PCFSoftShadowMap;
        }}
      >
        <Suspense fallback={<SceneLoader />}>
          {/* Lighting setup */}
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[50, 50, 25]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={100}
            shadow-camera-left={-50}
            shadow-camera-right={50}
            shadow-camera-top={50}
            shadow-camera-bottom={-50}
          />
          
          {/* Environment */}
          <Environment preset={environmentPreset as any} />
          <Sky sunPosition={[100, 20, 100]} />
          
          {/* Scene objects */}
          {layers.find(l => l.id === "terrain")?.visible && (
            <TerrainMesh project={project} />
          )}
          
          {layers.find(l => l.id === "structure")?.visible && (
            <ConstructionBuilding project={project} />
          )}
          
          {layers.find(l => l.id === "annotations")?.visible && (
            <AnnotationMarkers 
              annotations={project?.annotations || []}
              onAnnotationClick={onAnnotationClick}
              visible={true}
            />
          )}
          
          {/* Ground grid */}
          <Grid 
            args={[100, 100]} 
            position={[0, -2, 0]}
            cellSize={5}
            cellThickness={0.5}
            cellColor="#6B7280"
            sectionSize={25}
            sectionThickness={1}
            sectionColor="#374151"
            fadeDistance={100}
            fadeStrength={1}
          />
          
          {/* Contact shadows for realism */}
          <ContactShadows 
            position={[0, -1.99, 0]} 
            opacity={0.4} 
            scale={100} 
            blur={2} 
            far={20} 
          />
          
          {/* Camera controls */}
          <CameraController />

          {/* Advanced tools */}
          <ConstructionMeasurementTool
            isActive={selectedTool === "measure"}
            onMeasurementComplete={(measurement) => {
              console.log("Measurement completed:", measurement);
            }}
          />

          <ConstructionTeamPresence
            projectId={project?.id || ""}
            currentUserId={currentUserId}
          />

          {/* Performance stats (development) */}
          {showStats && <Stats />}
        </Suspense>
      </Canvas>
      
      {/* Development toggle for stats */}
      {process.env.NODE_ENV === 'development' && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 left-2 opacity-50 hover:opacity-100"
          onClick={() => setShowStats(!showStats)}
        >
          Stats
        </Button>
      )}
    </div>
  );
}
