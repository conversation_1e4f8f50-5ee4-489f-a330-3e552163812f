"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@midday/ui/card";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { Badge } from "@midday/ui/badge";
import {
  Box,
  Move3D,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Layers,
  Eye,
  EyeOff,
  Ruler,
  MessageSquare,
  Camera,
  Upload,
  Settings,
  Maximize,
  Grid3X3,
  Sun,
  Moon
} from "lucide-react";
import { useState, useRef, useEffect, Suspense } from "react";
import { Construction3DScene } from "./construction-3d-scene";
import type { RouterOutputs } from "@api/trpc/routers/_app";

type ConstructionProject = RouterOutputs["constructionProjects"]["getById"];
type Annotation = NonNullable<ConstructionProject>["annotations"][0];

interface Props {
  project: ConstructionProject;
  onAnnotationClick?: (annotation: Annotation) => void;
  onAreaSelect?: (coordinates: { x: number; y: number; z?: number }) => void;
  className?: string;
}

interface ViewerMode {
  mode: "navigate" | "measure" | "annotate" | "photo";
  icon: React.ReactNode;
  label: string;
}

const viewerModes: ViewerMode[] = [
  { mode: "navigate", icon: <Move3D className="h-4 w-4" />, label: "Navigate" },
  { mode: "measure", icon: <Ruler className="h-4 w-4" />, label: "Measure" },
  { mode: "annotate", icon: <MessageSquare className="h-4 w-4" />, label: "Annotate" },
  { mode: "photo", icon: <Camera className="h-4 w-4" />, label: "Photo" },
];

const layerTypes = [
  { id: "terrain", label: "Terrain", visible: true },
  { id: "structure", label: "Structure", visible: true },
  { id: "equipment", label: "Equipment", visible: true },
  { id: "annotations", label: "Annotations", visible: true },
  { id: "measurements", label: "Measurements", visible: false },
];

export function Construction3DViewer({ project, onAnnotationClick, onAreaSelect }: Props) {
  const [currentMode, setCurrentMode] = useState<ViewerMode["mode"]>("navigate");
  const [layers, setLayers] = useState(layerTypes);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAnnotation, setSelectedAnnotation] = useState<string | null>(null);

  const toggleLayer = (layerId: string) => {
    setLayers(prev => 
      prev.map(layer => 
        layer.id === layerId 
          ? { ...layer, visible: !layer.visible }
          : layer
      )
    );
  };

  const handleViewerClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (currentMode === "navigate") return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Convert screen coordinates to 3D world coordinates
    // This would be properly implemented with Three.js
    const worldCoords = {
      x: (x / rect.width) * 100,
      y: (y / rect.height) * 100,
      z: 0,
    };

    onAreaSelect?.(worldCoords);
  };

  const handleAnnotationClick = (annotation: Annotation) => {
    setSelectedAnnotation(annotation.id);
    onAnnotationClick?.(annotation);
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">3D Project View</CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-1" />
              Upload Model
            </Button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {viewerModes.map((mode) => (
              <Button
                key={mode.mode}
                variant={currentMode === mode.mode ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentMode(mode.mode)}
                className="h-8"
              >
                {mode.icon}
                <span className="ml-1 hidden sm:inline">{mode.label}</span>
              </Button>
            ))}
          </div>

          <div className="flex items-center space-x-1">
            <Button variant="outline" size="sm">
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="relative">
          {/* 3D Viewer Area */}
          <div
            className="h-96 relative overflow-hidden rounded-lg"
            onClick={handleViewerClick}
          >
            {/* Three.js Scene */}
            <Suspense fallback={
              <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-b from-blue-50 to-green-50 dark:from-gray-900 dark:to-gray-800">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
                  <p className="text-sm text-muted-foreground">Loading 3D scene...</p>
                </div>
              </div>
            }>
              <Construction3DScene
                project={project}
                onAnnotationClick={onAnnotationClick}
                layers={layers}
                selectedTool={currentMode}
                environmentPreset="sunset"
                currentUserId="current-user"
              />
            </Suspense>

            {/* Mode Indicator */}
            <div className="absolute top-2 left-2">
              <Badge variant="secondary" className="bg-white/90 backdrop-blur">
                {viewerModes.find(m => m.mode === currentMode)?.label} Mode
              </Badge>
            </div>

            {/* Annotations are now handled in the 3D scene */}

            {/* Layer Controls */}
            <div className="absolute top-2 right-2 space-y-1">
              <div className="bg-white/90 backdrop-blur rounded-lg p-2 space-y-1">
                <div className="flex items-center text-xs font-medium mb-1">
                  <Layers className="h-3 w-3 mr-1" />
                  Layers
                </div>
                {layers.map((layer) => (
                  <div key={layer.id} className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-5 w-5 p-0"
                      onClick={() => toggleLayer(layer.id)}
                    >
                      {layer.visible ? (
                        <Eye className="h-3 w-3" />
                      ) : (
                        <EyeOff className="h-3 w-3" />
                      )}
                    </Button>
                    <span className="text-xs">{layer.label}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Coordinates Display */}
            <div className="absolute bottom-2 left-2">
              <div className="bg-white/90 backdrop-blur rounded px-2 py-1 text-xs">
                Camera: X: 0 Y: 0 Z: 10
              </div>
            </div>

            {/* Compass */}
            <div className="absolute bottom-2 right-2">
              <div className="bg-white/90 backdrop-blur rounded-lg w-12 h-12 flex items-center justify-center">
                <div className="text-xs font-bold">N</div>
                <div className="absolute w-6 h-6 border-2 border-gray-400 rounded-full"></div>
                <div className="absolute w-3 h-0.5 bg-red-500 rounded"></div>
              </div>
            </div>
          </div>

          {/* Project Info Panel */}
          <div className="p-4 border-t bg-gray-50 dark:bg-gray-900">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Progress</div>
                <div className="font-medium">{project?.completionPercentage || 0}%</div>
              </div>
              <div>
                <div className="text-muted-foreground">Phase</div>
                <div className="font-medium">{project?.currentPhase || "N/A"}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Site Area</div>
                <div className="font-medium">
                  {project?.siteArea ? `${project.siteArea.toLocaleString()} sq ft` : "N/A"}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Annotations</div>
                <div className="font-medium">{project?.annotations?.length || 0}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}