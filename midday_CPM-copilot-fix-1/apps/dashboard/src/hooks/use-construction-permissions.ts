"use client";

import { useMemo } from "react";
import { 
  ConstructionRole, 
  Permission, 
  hasPermission, 
  canUserAccess, 
  getUserPermissions,
  getContextualPermissions,
  shouldShowFeature,
  getFilteredMenuItems
} from "@/lib/construction-roles";

interface UseConstructionPermissionsProps {
  userRole: ConstructionRole;
  projectContext?: {
    isProjectOwner?: boolean;
    isTeamMember?: boolean;
    projectPhase?: string;
    projectId?: string;
  };
}

export function useConstructionPermissions({ 
  userRole, 
  projectContext = {} 
}: UseConstructionPermissionsProps) {
  
  // Get base permissions for the user role
  const basePermissions = useMemo(() => {
    return getUserPermissions(userRole);
  }, [userRole]);
  
  // Get contextual permissions (includes project-specific permissions)
  const contextualPermissions = useMemo(() => {
    return getContextualPermissions(userRole, projectContext);
  }, [userRole, projectContext]);
  
  // Permission checking functions
  const checkPermission = useMemo(() => {
    return (permission: Permission) => hasPermission(userRole, permission);
  }, [userRole]);
  
  const checkMultiplePermissions = useMemo(() => {
    return (permissions: Permission[]) => canUserAccess(userRole, permissions);
  }, [userRole]);
  
  // Feature visibility functions
  const canShowFeature = useMemo(() => {
    return (feature: string) => shouldShowFeature(userRole, feature);
  }, [userRole]);
  
  // Menu filtering
  const filteredMenuItems = useMemo(() => {
    return getFilteredMenuItems(userRole);
  }, [userRole]);
  
  // Specific permission checks for common actions
  const permissions = useMemo(() => ({
    // Project management
    canEditProject: checkPermission("edit_project"),
    canDeleteProject: checkPermission("delete_project"),
    canManageTeam: checkPermission("manage_team"),
    
    // File management
    canUploadFiles: checkPermission("upload_files"),
    canDeleteFiles: checkPermission("delete_files"),
    
    // Communication
    canAddComments: checkPermission("add_comments"),
    canEditComments: checkPermission("edit_comments"),
    
    // Progress tracking
    canUpdateProgress: checkPermission("update_progress"),
    canApproveMilestones: checkPermission("approve_milestones"),
    
    // Financial access
    canViewFinancials: checkPermission("view_financials"),
    canEditFinancials: checkPermission("edit_financials"),
    
    // Reporting
    canCreateReports: checkPermission("create_reports"),
    
    // Scheduling
    canManageSchedule: checkPermission("manage_schedule"),
    
    // Safety and quality
    canOversightSafety: checkPermission("safety_oversight"),
    canQualityControl: checkPermission("quality_control"),
    
    // Client communication
    canClientCommunication: checkPermission("client_communication")
  }), [checkPermission]);
  
  // Feature visibility flags
  const features = useMemo(() => ({
    showFinancialDashboard: canShowFeature("financial_dashboard"),
    showTeamManagement: canShowFeature("team_management"),
    showProjectSettings: canShowFeature("project_settings"),
    showFileUpload: canShowFeature("file_upload"),
    showProgressUpdates: canShowFeature("progress_updates"),
    showMilestoneApproval: canShowFeature("milestone_approval"),
    showSafetyReports: canShowFeature("safety_reports"),
    showQualityControl: canShowFeature("quality_control")
  }), [canShowFeature]);
  
  // Role-based UI customization
  const uiConfig = useMemo(() => {
    const isManager = userRole === "project_manager";
    const isEngineer = userRole === "site_engineer";
    const isClient = userRole === "client";
    const isWorker = userRole === "worker";
    
    return {
      // Dashboard layout preferences
      showDetailedAnalytics: isManager || isEngineer,
      showSimplifiedView: isClient || isWorker,
      
      // Default tab preferences
      defaultTab: isManager ? "overview" : 
                 isEngineer ? "progress" :
                 isClient ? "overview" : "progress",
      
      // Notification preferences
      showAllNotifications: isManager,
      showRelevantNotifications: !isManager,
      
      // Action button visibility
      showPrimaryActions: isManager || isEngineer,
      showSecondaryActions: !isWorker,
      
      // Data visibility
      showSensitiveData: isManager,
      showPublicData: true
    };
  }, [userRole]);
  
  // Helper function to check if user can perform a complex action
  const canPerformAction = useMemo(() => {
    return (action: string, context?: any) => {
      switch (action) {
        case "create_milestone":
          return permissions.canUpdateProgress && permissions.canManageSchedule;
        
        case "approve_payment":
          return permissions.canViewFinancials && permissions.canApproveMilestones;
        
        case "assign_task":
          return permissions.canManageTeam || permissions.canManageSchedule;
        
        case "close_project":
          return permissions.canEditProject && permissions.canApproveMilestones;
        
        case "export_data":
          return permissions.canCreateReports;
        
        case "invite_member":
          return permissions.canManageTeam;
        
        case "change_project_phase":
          return permissions.canEditProject && permissions.canApproveMilestones;
        
        default:
          return false;
      }
    };
  }, [permissions]);
  
  return {
    userRole,
    basePermissions,
    contextualPermissions,
    permissions,
    features,
    uiConfig,
    filteredMenuItems,
    
    // Helper functions
    checkPermission,
    checkMultiplePermissions,
    canShowFeature,
    canPerformAction,
    
    // Utility functions
    hasAnyPermission: (perms: Permission[]) => perms.some(p => checkPermission(p)),
    hasAllPermissions: (perms: Permission[]) => perms.every(p => checkPermission(p)),
    
    // Role checking
    isManager: userRole === "project_manager",
    isEngineer: userRole === "site_engineer",
    isArchitect: userRole === "architect",
    isContractor: userRole === "contractor" || userRole === "subcontractor",
    isClient: userRole === "client",
    isInspector: userRole === "inspector",
    isSafetyOfficer: userRole === "safety_officer",
    isWorker: userRole === "worker" || userRole === "foreman"
  };
}
