# Construction Project Management System

## Overview

The Construction Project Management (CPM) system is a comprehensive solution built on top of Midday's financial platform, providing specialized tools for construction project management, real-time collaboration, and 3D visualization.

## Features

### 🏗️ Core Construction Features
- **Interactive Site Viewer**: Google Maps-based site visualization with building markers, progress indicators, and equipment tracking
- **Real-time Collaboration**: Live team presence, chat, activity feeds, and collaborative annotations
- **Progress Tracking**: Comprehensive analytics with timeline tracking, work area management, and cut/fill calculations
- **File Management**: Secure upload and organization of CAD files, blueprints, photos, and documents
- **Role-based Access Control**: Granular permissions system for different construction team roles

### 📱 Mobile-First Design
- **Responsive Interface**: Optimized for field workers and on-site usage
- **Touch-Optimized Controls**: Gesture support and mobile-friendly interactions
- **Offline Capabilities**: Core functionality available without internet connection
- **Voice Input**: Hands-free data entry for field conditions

### 🔐 Security & Permissions
- **Role-based Access**: 10 distinct construction roles with specific permissions
- **Contextual Permissions**: Dynamic permissions based on project phase and user context
- **Data Protection**: Secure file storage and encrypted communications

## Architecture

### Database Schema

The construction system extends Midday's existing database with specialized tables:

```sql
-- Core construction projects
construction_projects (
  id, name, description, location, status, 
  start_date, end_date, completion_percentage,
  created_at, updated_at
)

-- Site measurements and geospatial data
site_measurements (
  id, project_id, measurement_type, value, unit,
  coordinates, elevation, created_at
)

-- File management
construction_files (
  id, project_id, file_name, file_type, file_size,
  file_url, uploaded_by, created_at
)

-- Team and collaboration
project_team_members (
  id, project_id, user_id, role, permissions,
  joined_at, status
)

-- Real-time activities
construction_activities (
  id, project_id, user_id, activity_type,
  description, metadata, created_at
)
```

### API Layer (tRPC)

The system uses tRPC routers for type-safe API communication:

- `constructionProjects`: CRUD operations for projects
- `siteMeasurements`: Geospatial data management
- `constructionFiles`: File upload and management
- `teamCollaboration`: Real-time features and team management

### Component Architecture

```
src/components/construction/
├── site-viewer.tsx          # Main 3D/map visualization
├── project-details.tsx      # Left panel project information
├── analytics.tsx            # Bottom panel analytics and charts
├── team-presence.tsx        # Real-time team collaboration
├── activity-feed.tsx        # Live activity updates
├── mobile-nav.tsx           # Mobile navigation and responsive UI
├── role-guard.tsx           # Permission-based component wrapper
└── __tests__/               # Comprehensive test suite
```

## Role-Based Access Control

### Construction Roles

1. **Project Manager** - Full project oversight and management
2. **Site Engineer** - Technical oversight and engineering support
3. **Architect** - Design and architectural oversight
4. **Contractor** - Primary construction contractor
5. **Subcontractor** - Specialized trade contractor
6. **Client** - Project owner and stakeholder
7. **Inspector** - Quality and compliance inspector
8. **Safety Officer** - Safety oversight and compliance
9. **Foreman** - On-site crew supervisor
10. **Worker** - Construction worker

### Permission System

Each role has specific permissions:

```typescript
type Permission = 
  | "view_project" | "edit_project" | "delete_project"
  | "manage_team" | "upload_files" | "delete_files"
  | "add_comments" | "edit_comments" | "update_progress"
  | "approve_milestones" | "view_financials" | "edit_financials"
  | "create_reports" | "manage_schedule" | "safety_oversight"
  | "quality_control" | "client_communication";
```

### Usage Examples

```tsx
// Role-based component rendering
<RoleGuard userRole={userRole} requiredPermissions={["edit_project"]}>
  <ProjectEditForm />
</RoleGuard>

// Convenience components
<ManagerOnly userRole={userRole}>
  <AdminPanel />
</ManagerOnly>

<CanViewFinancials userRole={userRole}>
  <FinancialDashboard />
</CanViewFinancials>
```

## Real-time Features

### Team Presence
- Live user status and location tracking
- Current activity indicators
- Voice and video call integration

### Activity Feed
- Real-time project updates
- File upload notifications
- Progress milestone alerts
- System-generated insights

### Collaborative Chat
- Project-wide team communication
- Threaded conversations
- File sharing and annotations
- @mentions and notifications

## Mobile Responsiveness

### Responsive Breakpoints
- **Mobile**: < 768px - Full mobile experience with bottom navigation
- **Tablet**: 768px - 1024px - Hybrid layout with collapsible panels
- **Desktop**: > 1024px - Full three-panel layout

### Mobile Features
- **Sheet-based Navigation**: Slide-up panels for project details
- **Touch Gestures**: Pinch-to-zoom, swipe navigation
- **Optimized Controls**: Larger touch targets, simplified UI
- **Offline Support**: Core functionality without network

## Testing Strategy

### Unit Tests
- Role permission system validation
- Component rendering with different user roles
- Permission guard functionality
- Utility function testing

### Integration Tests
- API endpoint testing
- Database operations
- File upload workflows
- Real-time collaboration features

### E2E Tests
- Complete user workflows
- Cross-browser compatibility
- Mobile device testing
- Performance benchmarks

## Performance Optimization

### 3D Rendering
- Level-of-detail (LOD) system for complex models
- Progressive loading of site data
- Efficient marker clustering
- 60fps target for smooth interactions

### Data Loading
- Lazy loading of non-critical components
- Optimistic updates for real-time features
- Efficient caching strategies
- Background data synchronization

## Deployment & Configuration

### Environment Variables
```env
GOOGLE_MAPS_API_KEY=your_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
CONSTRUCTION_FILE_STORAGE_BUCKET=construction-files
```

### Feature Flags
- `ENABLE_3D_VISUALIZATION`: Toggle 3D model support
- `ENABLE_REAL_TIME_CHAT`: Enable/disable chat features
- `ENABLE_MOBILE_OFFLINE`: Offline functionality
- `ENABLE_VOICE_INPUT`: Voice recognition features

## API Documentation

### Construction Projects

```typescript
// Get project details
const project = await trpc.constructionProjects.getById.query({ id: "project-id" });

// Update project progress
await trpc.constructionProjects.updateProgress.mutate({
  id: "project-id",
  progress: 75,
  notes: "Foundation completed"
});

// Get team presence
const presence = await trpc.constructionProjects.getTeamPresence.query({
  projectId: "project-id"
});
```

### File Management

```typescript
// Upload construction file
const file = await trpc.constructionFiles.upload.mutate({
  projectId: "project-id",
  file: fileData,
  type: "blueprint",
  category: "architectural"
});

// Get project files
const files = await trpc.constructionFiles.getByProject.query({
  projectId: "project-id",
  type: "all" // or specific type
});
```

## Best Practices

### Security
- Always validate user permissions before rendering sensitive components
- Use role guards for API endpoints
- Implement proper file upload validation
- Sanitize user inputs and file uploads

### Performance
- Lazy load heavy components
- Use React.memo for expensive renders
- Implement proper loading states
- Optimize images and 3D models

### Accessibility
- Provide keyboard navigation
- Include proper ARIA labels
- Support screen readers
- Maintain color contrast ratios

### Mobile UX
- Design for touch-first interactions
- Provide haptic feedback where appropriate
- Optimize for various screen sizes
- Consider network limitations

## Troubleshooting

### Common Issues

1. **Google Maps not loading**
   - Check API key configuration
   - Verify domain restrictions
   - Ensure Maps JavaScript API is enabled

2. **Real-time features not working**
   - Verify Supabase configuration
   - Check WebSocket connections
   - Validate user authentication

3. **File uploads failing**
   - Check storage bucket permissions
   - Verify file size limits
   - Validate file type restrictions

4. **Permission errors**
   - Verify user role assignment
   - Check permission configuration
   - Validate context-specific permissions

## Future Enhancements

### Planned Features
- **AI-Powered Analytics**: Automated progress analysis and delay prediction
- **IoT Integration**: Sensor data from construction equipment
- **Advanced 3D Visualization**: BIM model integration and AR support
- **Automated Reporting**: AI-generated progress reports and insights
- **Advanced Scheduling**: Critical path analysis and resource optimization

### Technical Improvements
- **WebRTC Integration**: Direct peer-to-peer communication
- **Advanced Caching**: Service worker implementation
- **Performance Monitoring**: Real-time performance analytics
- **Advanced Security**: Multi-factor authentication and audit logging
