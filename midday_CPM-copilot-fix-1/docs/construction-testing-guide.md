# Construction System Testing Guide

## Manual Testing Checklist

### 1. Basic Navigation
- [ ] Navigate to `/construction` - should show construction projects list
- [ ] Click on a project - should open project detail page
- [ ] Verify three-panel layout (desktop) or mobile navigation (mobile)
- [ ] Test back navigation functionality

### 2. Site Viewer Testing
- [ ] Google Maps loads correctly
- [ ] Building markers are visible and clickable
- [ ] Equipment markers show when enabled
- [ ] Progress indicators display correctly
- [ ] Map type switching (Satellite/Roadmap) works
- [ ] Zoom controls function properly
- [ ] Legend displays correctly (desktop only)

### 3. Project Details Panel
- [ ] Project information displays correctly
- [ ] Progress percentage shows accurate data
- [ ] Key metrics are visible and formatted properly
- [ ] File upload section is accessible (if user has permissions)
- [ ] Project status and timeline information is accurate

### 4. Analytics Panel
- [ ] Timeline tab shows project phases with progress
- [ ] Progress tab displays work area completion
- [ ] Daily chart shows progress over time
- [ ] Work areas tab shows detailed area progress with team info
- [ ] Resources tab displays resource allocation pie chart

### 5. Team Presence & Collaboration
- [ ] Team members list shows online/offline status
- [ ] Current activity and location information displays
- [ ] Chat functionality works (send/receive messages)
- [ ] Activity feed shows real-time updates
- [ ] Voice/video call buttons are functional (if implemented)

### 6. Role-Based Access Control
Test with different user roles:

#### Project Manager
- [ ] Can access all features
- [ ] Can see financial information
- [ ] Can manage team members
- [ ] Can edit project settings

#### Site Engineer
- [ ] Can access technical features
- [ ] Can update progress
- [ ] Can upload files
- [ ] Cannot access financial data

#### Client
- [ ] Can view project overview
- [ ] Can see financial information
- [ ] Cannot edit project details
- [ ] Cannot manage team

#### Worker
- [ ] Can view basic project information
- [ ] Can add comments
- [ ] Cannot access management features
- [ ] Cannot view sensitive data

### 7. Mobile Responsiveness
- [ ] Mobile header displays correctly
- [ ] Bottom navigation works properly
- [ ] Sheet-based panels slide up correctly
- [ ] Touch interactions work smoothly
- [ ] Content is readable on small screens
- [ ] All features accessible via mobile interface

### 8. Real-time Features
- [ ] Activity feed updates in real-time
- [ ] Team presence status changes reflect immediately
- [ ] Chat messages appear instantly
- [ ] Progress updates sync across users
- [ ] File uploads trigger notifications

### 9. File Management
- [ ] File upload works correctly
- [ ] Different file types are supported
- [ ] File preview/download functions
- [ ] File organization by category
- [ ] Permission-based file access

### 10. Performance Testing
- [ ] Initial page load < 3 seconds
- [ ] Map interactions are smooth (60fps target)
- [ ] Large file uploads don't block UI
- [ ] Real-time updates don't cause lag
- [ ] Mobile performance is acceptable

## Automated Testing

### Unit Tests
```bash
# Run construction role tests
npm test -- construction-roles.test.ts

# Run role guard component tests  
npm test -- role-guard.test.tsx

# Run all construction tests
npm test -- construction/
```

### Integration Tests
```bash
# Test API endpoints
npm run test:api

# Test database operations
npm run test:db

# Test file upload workflows
npm run test:files
```

### E2E Tests
```bash
# Run full user workflow tests
npm run test:e2e

# Test mobile workflows
npm run test:e2e:mobile

# Test cross-browser compatibility
npm run test:e2e:cross-browser
```

## Test Data Setup

### Sample Project Data
```sql
INSERT INTO construction_projects (id, name, description, location, status, completion_percentage)
VALUES (
  '133d080b-f460-41fd-be6c-faf0552165ce',
  'Downtown Office Complex',
  'Modern office building with retail space',
  'Downtown Business District',
  'in_progress',
  66
);
```

### Sample Team Members
```sql
INSERT INTO project_team_members (project_id, user_id, role, permissions)
VALUES 
  ('133d080b-f460-41fd-be6c-faf0552165ce', 'user-1', 'project_manager', '["manage_team", "edit_project"]'),
  ('133d080b-f460-41fd-be6c-faf0552165ce', 'user-2', 'site_engineer', '["update_progress", "upload_files"]'),
  ('133d080b-f460-41fd-be6c-faf0552165ce', 'user-3', 'contractor', '["view_project", "add_comments"]');
```

## Common Issues & Solutions

### Google Maps Not Loading
**Issue**: Map container shows blank or error
**Solution**: 
- Verify `GOOGLE_MAPS_API_KEY` is set correctly
- Check API key has Maps JavaScript API enabled
- Ensure domain is whitelisted in Google Cloud Console

### Real-time Features Not Working
**Issue**: Activity feed or chat not updating
**Solution**:
- Check Supabase configuration
- Verify WebSocket connections
- Test with multiple browser tabs

### Permission Errors
**Issue**: Users seeing content they shouldn't access
**Solution**:
- Verify role assignment in database
- Check permission configuration in code
- Test with different user roles

### Mobile Layout Issues
**Issue**: Components not responsive or touch-unfriendly
**Solution**:
- Test on actual mobile devices
- Verify CSS breakpoints
- Check touch target sizes (minimum 44px)

### Performance Issues
**Issue**: Slow loading or laggy interactions
**Solution**:
- Profile with browser dev tools
- Check for memory leaks
- Optimize large images/models
- Implement proper loading states

## Browser Compatibility

### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Mobile Browsers
- iOS Safari 14+
- Chrome Mobile 90+
- Samsung Internet 14+

### Known Issues
- IE11: Not supported (uses modern JavaScript features)
- Older Android browsers: Limited WebRTC support
- Safari < 14: Some CSS Grid features may not work

## Performance Benchmarks

### Target Metrics
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

### Mobile Performance
- **3G Network**: Page loads in < 5s
- **Touch Response**: < 50ms delay
- **Scroll Performance**: 60fps maintained
- **Battery Usage**: Minimal impact during normal use

## Security Testing

### Authentication
- [ ] Verify user authentication is required
- [ ] Test session timeout handling
- [ ] Check for proper logout functionality

### Authorization
- [ ] Test role-based access restrictions
- [ ] Verify permission boundaries
- [ ] Check for privilege escalation vulnerabilities

### Data Protection
- [ ] Verify file upload security
- [ ] Test input sanitization
- [ ] Check for XSS vulnerabilities
- [ ] Verify CSRF protection

### API Security
- [ ] Test rate limiting
- [ ] Verify input validation
- [ ] Check for SQL injection protection
- [ ] Test API authentication

## Accessibility Testing

### WCAG Compliance
- [ ] Keyboard navigation works throughout
- [ ] Screen reader compatibility
- [ ] Color contrast meets AA standards
- [ ] Focus indicators are visible
- [ ] Alt text for images and icons

### Mobile Accessibility
- [ ] Voice control compatibility
- [ ] Large text support
- [ ] High contrast mode support
- [ ] Touch accommodation features

## Deployment Testing

### Environment Verification
- [ ] All environment variables set correctly
- [ ] Database connections working
- [ ] File storage accessible
- [ ] External APIs responding

### Feature Flags
- [ ] Test with different feature flag combinations
- [ ] Verify graceful degradation
- [ ] Check fallback behaviors

### Monitoring
- [ ] Error tracking configured
- [ ] Performance monitoring active
- [ ] User analytics collecting data
- [ ] Alerts configured for critical issues
