{"permissions": {"allow": ["Bash(grep:*)", "Bash(pnpm dev:*)", "Bash(bun:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run dev:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(source:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "Bash(psql:*)", "Bash(PGPASSWORD=\"Constru360!\" psql -h aws-0-ca-central-1.pooler.supabase.com -p 6543 -U postgres.hipvnsajejwkighxsvuz -d postgres -c \"\\d invoice_templates\")", "Bash(PGPASSWORD=\"Constru360!\" psql -h aws-0-ca-central-1.pooler.supabase.com -p 6543 -U postgres.hipvnsajejwkighxsvuz -d postgres -c \"\\d bank_accounts\")", "Bash(rg:*)", "Bash(ls:*)"], "deny": []}}