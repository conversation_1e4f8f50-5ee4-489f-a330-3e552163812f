name: Preview Deployment - Engine
env:
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
on:
  push:
    branches-ignore:
      - main
    paths:
      - apps/engine/**
jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install dependencies
        run: bun install
      - name: 🔦 Run linter
        run: bun run lint
        working-directory: ./apps/engine
      - name: 🪐 Check TypeScript
        run: bun run typecheck
        working-directory: ./apps/engine
      - name: 🧪 Run unit tests
        run: bun test
        working-directory: ./apps/engine
      - name: Deploy Project Artifacts to Cloudflare
        uses: cloudflare/wrangler-action@v3
        with:
          packageManager: bun
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          workingDirectory: "apps/engine"
          wranglerVersion: "3.93.0"
          command: deploy --minify src/index.ts --env staging
