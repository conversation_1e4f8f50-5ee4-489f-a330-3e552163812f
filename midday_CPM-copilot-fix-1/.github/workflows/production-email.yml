name: Production Deployment - Email
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_EMAIL }}
  TURBO_TOKEN: ${{ secrets.VERCEL_TOKEN }}
  TURBO_TEAM: ${{ secrets.VERCEL_ORG_ID }}
on:
  push:
    branches:
      - main
    paths:
      - packages/email/**
jobs:
  deploy-production:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install dependencies
        run: bun install
      - name: 🔦 Run linter
        run: bun run lint
        working-directory: packages/email
      - name: 🪐 Check TypeScript
        run: bun run typecheck
        working-directory: packages/email
      - name: 📤 Pull Vercel Environment Information
        run: bunx vercel env pull .env --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: 📤 Pull Vercel Environment Information
        run: bunx vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: 🏗 Build Project Artifacts
        run: bunx vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: |
          bunx vercel deploy --prebuilt --prod --archive=tgz --token=${{ secrets.VERCEL_TOKEN }} > domain.txt
          bunx vercel alias --scope=${{ secrets.VERCEL_ORG_ID }} --token=${{ secrets.VERCEL_TOKEN }} set `cat domain.txt` email.midday.ai
